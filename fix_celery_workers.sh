#!/bin/bash

set -e

echo "🔧 Celery Worker修复工具"
echo "========================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 1. 停止现有的Celery进程
echo ""
log_info "步骤1: 停止现有的Celery进程"
echo "----------------------------------------"

log_info "查找并停止现有Celery进程..."
docker exec km-background pkill -f celery || log_info "没有找到运行中的Celery进程"

sleep 5

# 2. 清理Redis中的Celery数据
echo ""
log_info "步骤2: 清理Redis中的Celery数据"
echo "----------------------------------------"

log_warning "即将清理Redis中的Celery队列数据"
read -p "确认继续？这将清空所有待处理的任务 (y/N): " confirm
if [[ $confirm =~ ^[Yy]$ ]]; then
    log_info "清理Celery相关数据..."
    docker exec km-background python3 -c "
import sys
sys.path.append('/app')

try:
    from onyx.configs.app_configs import REDIS_HOST, REDIS_PORT, REDIS_DB_NUMBER_CELERY
    import redis
    
    r = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB_NUMBER_CELERY)
    
    # 获取所有Celery相关的键
    celery_keys = r.keys('celery*')
    if celery_keys:
        print(f'找到 {len(celery_keys)} 个Celery相关键')
        deleted = r.delete(*celery_keys)
        print(f'删除了 {deleted} 个键')
    else:
        print('没有找到Celery相关键')
        
    # 清理其他可能的队列键
    queue_patterns = ['*queue*', '*task*', '*result*']
    for pattern in queue_patterns:
        keys = r.keys(pattern)
        if keys:
            print(f'清理 {pattern} 模式的键: {len(keys)} 个')
            r.delete(*keys)
    
    print('Redis清理完成')
    
except Exception as e:
    print(f'Redis清理失败: {e}')
" || log_error "Redis清理失败"
else
    log_info "跳过Redis清理"
fi

# 3. 重启background容器
echo ""
log_info "步骤3: 重启background容器"
echo "----------------------------------------"

log_info "重启background容器..."
docker compose -f docker-compose.main.yml restart background

log_info "等待容器启动..."
sleep 30

# 验证容器状态
if docker ps | grep -q km-background; then
    log_success "background容器重启成功"
else
    log_error "background容器重启失败"
    exit 1
fi

# 4. 检查自动启动的Worker
echo ""
log_info "步骤4: 检查自动启动的Worker"
echo "----------------------------------------"

log_info "等待Worker自动启动..."
sleep 15

log_info "检查Celery进程..."
if docker exec km-background ps aux | grep -q celery; then
    log_success "发现Celery进程"
    docker exec km-background ps aux | grep celery | grep -v grep
else
    log_warning "未发现自动启动的Celery进程"
    
    # 5. 手动启动Worker
    echo ""
    log_info "步骤5: 手动启动Celery Worker"
    echo "----------------------------------------"
    
    log_info "手动启动Celery worker..."
    
    # 启动主要的worker
    docker exec -d km-background celery -A onyx.background.celery.versioned_apps.primary worker \
        --loglevel=info \
        --concurrency=4 \
        --queues=celery_primary \
        --hostname=worker-primary@%h
    
    sleep 10
    
    # 启动其他队列的worker
    docker exec -d km-background celery -A onyx.background.celery.versioned_apps.primary worker \
        --loglevel=info \
        --concurrency=2 \
        --queues=vespa_metadata_sync \
        --hostname=worker-vespa@%h
    
    sleep 10
    
    log_info "检查手动启动的Worker..."
    if docker exec km-background ps aux | grep -q celery; then
        log_success "Worker手动启动成功"
        docker exec km-background ps aux | grep celery | grep -v grep
    else
        log_error "Worker手动启动失败"
    fi
fi

# 6. 测试Worker连接
echo ""
log_info "步骤6: 测试Worker连接"
echo "----------------------------------------"

log_info "等待Worker完全启动..."
sleep 20

log_info "测试Worker响应..."
docker exec km-background timeout 15 python3 -c "
import sys
sys.path.append('/app')

try:
    from onyx.background.celery.versioned_apps.primary import celery_app
    
    print('测试Celery连接...')
    
    # 测试inspect功能
    inspect = celery_app.control.inspect(timeout=10)
    
    # 检查活跃worker
    active = inspect.active()
    if active:
        print(f'✅ 活跃worker: {list(active.keys())}')
        for worker, tasks in active.items():
            print(f'  {worker}: {len(tasks)} 个活跃任务')
    else:
        print('❌ 没有活跃worker')
    
    # 检查注册的worker
    registered = inspect.registered()
    if registered:
        print(f'✅ 注册的worker: {list(registered.keys())}')
    else:
        print('❌ 没有注册的worker')
    
    # 检查worker统计
    stats = inspect.stats()
    if stats:
        print(f'✅ Worker统计信息可用')
        for worker, stat in stats.items():
            print(f'  {worker}: 总任务 {stat.get(\"total\", 0)}')
    else:
        print('❌ 无法获取worker统计信息')
        
except Exception as e:
    print(f'❌ Worker测试失败: {e}')
    import traceback
    traceback.print_exc()
" || log_warning "Worker连接测试失败或超时"

# 7. 发送测试任务
echo ""
log_info "步骤7: 发送测试任务"
echo "----------------------------------------"

log_info "发送测试任务验证Worker功能..."
docker exec km-background python3 -c "
import sys
sys.path.append('/app')

try:
    from onyx.background.celery.versioned_apps.primary import celery_app
    from onyx.configs.constants import OnyxCeleryTask
    
    # 发送一个简单的测试任务
    result = celery_app.send_task(
        'onyx.background.celery.tasks.beat.check_for_indexing',
        kwargs={'tenant_id': 'default'},
        queue='celery_primary'
    )
    
    print(f'✅ 测试任务已发送: {result.id}')
    
    # 等待一下看是否被处理
    import time
    time.sleep(5)
    
    # 检查任务状态
    if result.ready():
        print(f'✅ 任务已完成: {result.result}')
    else:
        print(f'⏳ 任务处理中: {result.state}')
        
except Exception as e:
    print(f'❌ 测试任务发送失败: {e}')
" || log_warning "测试任务发送失败"

# 8. 生成修复报告
echo ""
log_info "步骤8: 生成修复报告"
echo "----------------------------------------"

echo ""
log_success "Celery Worker修复完成！"
echo ""
echo "📋 修复总结:"
echo "1. ✅ 停止了现有的Celery进程"
echo "2. ✅ 清理了Redis中的Celery数据"
echo "3. ✅ 重启了background容器"
echo "4. ✅ 检查并启动了Worker进程"
echo "5. ✅ 测试了Worker连接"
echo "6. ✅ 发送了测试任务"

echo ""
echo "🔍 验证命令:"
echo "# 检查Worker状态"
echo "docker exec km-background celery -A onyx.background.celery.versioned_apps.primary inspect active"
echo ""
echo "# 查看Worker统计"
echo "docker exec km-background celery -A onyx.background.celery.versioned_apps.primary inspect stats"
echo ""
echo "# 查看实时日志"
echo "docker logs km-background -f | grep -E '(celery|worker|task)'"

echo ""
echo "📱 监控建议:"
echo "1. 定期检查Worker状态"
echo "2. 监控任务队列长度"
echo "3. 观察索引任务是否正常处理"
echo "4. 如果问题重现，检查系统资源和网络连接"
