#!/bin/bash

set -e

echo "🔧 清空并重新部署Vespa应用"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 1. 删除现有应用
log_info "步骤1: 删除现有Vespa应用"
echo "----------------------------------------"

log_warning "即将删除现有Vespa应用，这将清空所有索引数据"
read -p "确认继续？(y/N): " confirm
if [[ ! $confirm =~ ^[Yy]$ ]]; then
    log_info "操作已取消"
    exit 0
fi

log_info "删除现有应用..."
DELETE_RESULT=$(curl -X DELETE \
    "http://localhost:19071/application/v2/tenant/default/application/default" \
    -w "%{http_code}" -s -o /tmp/delete_response.txt)

echo "删除响应状态码: $DELETE_RESULT"
if [[ $DELETE_RESULT -eq 200 || $DELETE_RESULT -eq 404 ]]; then
    log_success "现有应用已删除或不存在"
else
    log_warning "删除应用可能失败，但继续部署"
    cat /tmp/delete_response.txt
fi

# 等待删除完成
log_info "等待删除操作完成..."
sleep 10

# 2. 创建新的应用包
log_info "步骤2: 创建新的Vespa应用包"
echo "----------------------------------------"

cat > /tmp/deploy_clean_vespa.py << 'EOF'
#!/usr/bin/env python3
import os
import zipfile
import requests
import time

def create_hosts_xml():
    return """<?xml version="1.0" encoding="utf-8" ?>
<hosts>
  <host name="localhost">
    <alias>danswer-node</alias>
  </host>
</hosts>"""

def create_services_xml():
    return """<?xml version="1.0" encoding="utf-8" ?>
<services version="1.0">
    <container id="default" version="1.0">
        <document-api/>
        <search/>
        <http>
            <server id="default" port="8080"/>
        </http>
        <nodes>
            <node hostalias="danswer-node" />
        </nodes>
    </container>
    <content id="danswer_index" version="1.0">
        <redundancy>1</redundancy>
        <documents>
            <document type="danswer_chunk" mode="index" />
        </documents>
        <nodes>
            <node hostalias="danswer-node" distribution-key="0" />
        </nodes>
        <tuning>
            <resource-limits>
                <disk>0.85</disk>
            </resource-limits>
        </tuning>
        <engine>    
            <proton>
                <tuning>
                    <searchnode>
                        <requestthreads>
                            <persearch>4</persearch>
                        </requestthreads>
                    </searchnode>
                </tuning>
            </proton>
        </engine>
    </content>
</services>"""

def create_schema():
    return """schema danswer_chunk {
    document danswer_chunk {
        field document_id type string {
            indexing: summary | attribute
            rank: filter
            attribute: fast-search
        }
        field chunk_id type int {
            indexing: summary | attribute
        }
        field semantic_identifier type string {
            indexing: summary | attribute
        }
        field title type string {
            indexing: summary | index | attribute
            index: enable-bm25
        }
        field content type string {
            indexing: summary | index
            index: enable-bm25
        }
        field content_summary type string {
            indexing: summary | index
            summary: dynamic
        }
        field title_embedding type tensor<float>(x[1024]) {
            indexing: attribute | index
            attribute {
                distance-metric: angular
            }
        }
        field embeddings type tensor<float>(t{},x[1024]) {
            indexing: attribute | index
            attribute {
                distance-metric: angular
            }
        }
        field source_type type string {
            indexing: summary | attribute
            rank: filter
            attribute: fast-search
        }
        field boost type float {
            indexing: summary | attribute
        }
        field hidden type bool {
            indexing: summary | attribute
            rank: filter
        }
        field metadata type string {
            indexing: summary | attribute
        }
        field doc_updated_at type int {
            indexing: summary | attribute
        }
        field access_control_list type weightedset<string> {
            indexing: summary | attribute
            rank: filter
            attribute: fast-search
        }
        field document_sets type weightedset<string> {
            indexing: summary | attribute
            rank: filter
            attribute: fast-search
        }
    }
    
    fieldset default {
        fields: content, title
    }
    
    rank-profile default {
        first-phase {
            expression: nativeRank(title, content)
        }
    }
    
    rank-profile hybrid_search_semantic_base_1024 {
        inputs {
            query(query_embedding) tensor<float>(x[1024])
            query(alpha) double: 0.5
            query(title_content_ratio) double: 0.5
        }
        
        first-phase {
            expression: query(title_content_ratio) * closeness(field, title_embedding) + (1 - query(title_content_ratio)) * closeness(field, embeddings)
        }
        
        global-phase {
            expression {
                (
                    query(alpha) * (
                        (query(title_content_ratio) * normalize_linear(closeness(field, title_embedding)))
                        +
                        ((1 - query(title_content_ratio)) * normalize_linear(closeness(field, embeddings)))
                    )
                    +
                    (1 - query(alpha)) * (
                        (query(title_content_ratio) * normalize_linear(bm25(title)))
                        +
                        ((1 - query(title_content_ratio)) * normalize_linear(bm25(content)))
                    )
                )
            }
            rerank-count: 1000
        }
    }
}"""

def main():
    print("📦 创建全新的Vespa应用包...")
    
    # 创建应用包目录
    app_dir = "/tmp/vespa-clean-app"
    os.makedirs(app_dir, exist_ok=True)
    os.makedirs(os.path.join(app_dir, "schemas"), exist_ok=True)
    
    # 创建文件（不包含validation-overrides.xml）
    with open(os.path.join(app_dir, "hosts.xml"), 'w') as f:
        f.write(create_hosts_xml())
    
    with open(os.path.join(app_dir, "services.xml"), 'w') as f:
        f.write(create_services_xml())
    
    with open(os.path.join(app_dir, "schemas", "danswer_chunk.sd"), 'w') as f:
        f.write(create_schema())
    
    print("✅ 应用包文件创建完成")
    
    # 打包
    zip_path = "/tmp/vespa-clean-app.zip"
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(app_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, app_dir)
                zipf.write(file_path, arcname)
    
    print(f"✅ 应用包已打包: {zip_path}")
    
    # 部署
    print("🚀 部署全新应用包到Vespa...")
    deploy_url = "http://localhost:19071/application/v2/tenant/default/prepareandactivate"
    
    try:
        with open(zip_path, 'rb') as f:
            response = requests.post(
                deploy_url,
                data=f.read(),
                headers={'Content-Type': 'application/zip'},
                timeout=300
            )
        
        print(f"部署响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 应用包部署成功")
            
            # 等待就绪
            print("⏳ 等待应用就绪...")
            max_wait = 300  # 5分钟
            elapsed = 0
            
            while elapsed < max_wait:
                try:
                    resp = requests.get("http://localhost:8081/ApplicationStatus", timeout=10)
                    if resp.status_code == 200:
                        print("✅ Vespa应用已就绪")
                        print("应用状态:")
                        print(resp.text[:300] + "..." if len(resp.text) > 300 else resp.text)
                        return 0
                except Exception as e:
                    pass
                
                time.sleep(10)
                elapsed += 10
                print(f"等待中... {elapsed}/{max_wait}秒")
            
            print("❌ 应用就绪超时")
            return 1
        else:
            print(f"❌ 部署失败: {response.status_code}")
            print(f"错误详情: {response.text}")
            return 1
            
    except Exception as e:
        print(f"❌ 部署过程中出错: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
EOF

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    log_error "需要Python3环境"
    exit 1
fi

# 安装requests包
log_info "检查Python依赖..."
python3 -c "import requests" 2>/dev/null || {
    log_info "安装requests包..."
    pip3 install requests 2>/dev/null || {
        log_warning "使用系统包管理器安装..."
        if command -v dnf &> /dev/null; then
            sudo dnf install -y python3-requests
        elif command -v apt &> /dev/null; then
            sudo apt update && sudo apt install -y python3-requests
        fi
    }
}

# 执行部署
log_info "执行全新Vespa应用包部署..."
python3 /tmp/deploy_clean_vespa.py

if [ $? -eq 0 ]; then
    log_success "Vespa应用包部署成功！"
    
    # 重启相关服务
    log_info "重启相关服务..."
    docker-compose -f docker-compose.main.yml restart background
    
    sleep 30
    
    # 验证修复结果
    log_info "验证修复结果..."
    echo ""
    echo "=== Vespa容器状态 ==="
    docker ps | grep km-vespa
    
    echo ""
    echo "=== Vespa健康检查 ==="
    if curl -s http://localhost:8081/ApplicationStatus | head -3; then
        log_success "Vespa健康检查通过"
    else
        log_warning "Vespa健康检查失败"
    fi
    
    echo ""
    echo "=== Background服务日志检查 ==="
    if docker logs km-background --tail=10 | grep -i vespa; then
        log_info "发现Vespa相关日志"
    else
        log_success "未发现Vespa错误日志"
    fi
    
    echo ""
    log_success "修复完成！"
    echo "📋 服务访问地址："
    echo "- Vespa搜索: http://**********:8081"
    echo "- API服务: http://**********:8080"
    echo "- Web前端: http://**********:3000"
    
    echo ""
    log_warning "注意：由于清空了Vespa应用，所有索引数据已丢失"
    log_info "系统恢复正常后，需要重新索引文档数据"
    
else
    log_error "Vespa应用包部署失败"
    echo ""
    echo "🔍 故障排除建议："
    echo "1. 检查Vespa容器日志: docker logs km-vespa --tail=50"
    echo "2. 检查配置服务器状态: curl http://localhost:19071/"
    echo "3. 重启Vespa容器: docker-compose -f docker-compose.main.yml restart vespa"
    exit 1
fi
