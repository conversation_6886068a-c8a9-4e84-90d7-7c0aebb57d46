#!/bin/bash

set -e

echo "🔍 索引任务修复版诊断工具"
echo "=========================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 1. 检查Celery worker连接
echo ""
log_info "步骤1: 检查Celery worker连接"
echo "----------------------------------------"

log_info "测试docfetching worker连接..."
docker exec km-background timeout 10 celery -A onyx.background.celery.versioned_apps.docfetching inspect ping || log_warning "docfetching worker无响应"

log_info "测试primary worker连接..."
docker exec km-background timeout 10 celery -A onyx.background.celery.versioned_apps.primary inspect ping || log_warning "primary worker无响应"

# 2. 检查队列状态
echo ""
log_info "步骤2: 检查Redis队列状态"
echo "----------------------------------------"

log_info "检查队列长度..."
docker exec km-background python3 -c "
import sys
sys.path.append('/app')

try:
    from onyx.configs.app_configs import REDIS_HOST, REDIS_PORT, REDIS_DB_NUMBER_CELERY
    import redis
    
    r = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB_NUMBER_CELERY)
    
    # 检查主要队列
    queues = [
        'connector_doc_fetching',
        'celery', 
        'vespa_metadata_sync',
        'docprocessing'
    ]
    
    print('队列状态:')
    for queue in queues:
        length = r.llen(queue)
        print(f'{queue}: {length} 个任务')
    
    # 检查所有Celery相关键
    all_keys = r.keys('*')
    celery_keys = [k.decode() for k in all_keys if 'celery' in k.decode().lower()]
    print(f'\\nCelery相关键数量: {len(celery_keys)}')
    
    # 检查是否有任务结果
    result_keys = [k.decode() for k in all_keys if 'result' in k.decode().lower()]
    print(f'任务结果键数量: {len(result_keys)}')
    
except Exception as e:
    print(f'队列检查失败: {e}')
    import traceback
    traceback.print_exc()
" || log_error "队列状态检查失败"

# 3. 检查数据库连接和索引任务
echo ""
log_info "步骤3: 检查数据库和索引任务"
echo "----------------------------------------"

log_info "初始化数据库并查询索引任务..."
docker exec km-background python3 -c "
import sys
sys.path.append('/app')

try:
    # 初始化数据库引擎
    from onyx.db.engine.sql_engine import SqlEngine
    from onyx.configs.app_configs import POSTGRES_HOST, POSTGRES_PORT, POSTGRES_USER, POSTGRES_PASSWORD, POSTGRES_DB
    
    # 初始化数据库连接
    SqlEngine.init_engine(
        database_url=f'postgresql://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}'
    )
    
    from onyx.db.engine.sql_engine import get_session_with_current_tenant
    from onyx.db.models import IndexAttempt, ConnectorCredentialPair, Connector
    from onyx.db.enums import IndexingStatus, ConnectorCredentialPairStatus
    from sqlalchemy import desc, and_, func
    from datetime import datetime, timedelta
    
    with get_session_with_current_tenant() as db_session:
        # 查询NOT_STARTED状态的任务
        not_started_tasks = db_session.query(IndexAttempt).filter(
            IndexAttempt.status == IndexingStatus.NOT_STARTED
        ).order_by(desc(IndexAttempt.time_created)).limit(5).all()
        
        print('NOT_STARTED状态的索引任务:')
        print('ID\\t创建时间\\t\\t\\tCelery任务ID\\t\\t连接器ID')
        print('-' * 80)
        for task in not_started_tasks:
            created = task.time_created.strftime('%m-%d %H:%M:%S') if task.time_created else 'None'
            celery_id = (task.celery_task_id[:20] + '...') if task.celery_task_id else 'None'
            print(f'{task.id}\\t{created}\\t{celery_id}\\t{task.connector_credential_pair_id}')
        
        # 统计任务状态
        status_counts = db_session.query(
            IndexAttempt.status, func.count(IndexAttempt.id)
        ).group_by(IndexAttempt.status).all()
        
        print('\\n任务状态统计:')
        for status, count in status_counts:
            print(f'{status.value}: {count}')
        
        # 检查活跃连接器
        active_connectors = db_session.query(ConnectorCredentialPair).filter(
            ConnectorCredentialPair.status == ConnectorCredentialPairStatus.ACTIVE
        ).count()
        
        print(f'\\n活跃连接器数量: {active_connectors}')
        
        # 检查最近1小时创建的任务
        recent_cutoff = datetime.utcnow() - timedelta(hours=1)
        recent_tasks = db_session.query(IndexAttempt).filter(
            IndexAttempt.time_created >= recent_cutoff
        ).count()
        
        print(f'最近1小时创建的任务: {recent_tasks}')
        
except Exception as e:
    print(f'数据库查询失败: {e}')
    import traceback
    traceback.print_exc()
" || log_error "数据库查询失败"

# 4. 手动触发索引检查任务
echo ""
log_info "步骤4: 手动触发索引检查任务"
echo "----------------------------------------"

log_info "发送check_for_indexing任务..."
docker exec km-background python3 -c "
import sys
sys.path.append('/app')

try:
    # 使用正确的导入路径
    from onyx.background.celery.apps.primary import celery_app
    from onyx.configs.constants import OnyxCeleryTask, OnyxCeleryQueues, OnyxCeleryPriority
    
    print(f'Celery应用: {celery_app.main}')
    print(f'Broker URL: {celery_app.conf.broker_url}')
    
    # 发送任务
    result = celery_app.send_task(
        OnyxCeleryTask.CHECK_FOR_INDEXING,
        kwargs={'tenant_id': 'default'},
        queue=OnyxCeleryQueues.CELERY_PRIMARY,
        priority=OnyxCeleryPriority.HIGH
    )
    
    print(f'✅ check_for_indexing任务已发送: {result.id}')
    
    # 等待一下看结果
    import time
    time.sleep(15)
    
    try:
        if result.ready():
            print(f'✅ 任务已完成，结果: {result.result}')
        else:
            print(f'⏳ 任务处理中，状态: {result.state}')
    except Exception as e:
        print(f'获取任务结果失败: {e}')
        
except Exception as e:
    print(f'❌ 任务发送失败: {e}')
    import traceback
    traceback.print_exc()
" || log_warning "check_for_indexing任务测试失败"

# 5. 检查连接器配置
echo ""
log_info "步骤5: 检查连接器配置"
echo "----------------------------------------"

log_info "查询连接器配置..."
docker exec km-background python3 -c "
import sys
sys.path.append('/app')

try:
    # 初始化数据库
    from onyx.db.engine.sql_engine import SqlEngine
    from onyx.configs.app_configs import POSTGRES_HOST, POSTGRES_PORT, POSTGRES_USER, POSTGRES_PASSWORD, POSTGRES_DB
    
    SqlEngine.init_engine(
        database_url=f'postgresql://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}'
    )
    
    from onyx.db.engine.sql_engine import get_session_with_current_tenant
    from onyx.db.models import ConnectorCredentialPair, Connector
    from onyx.db.enums import ConnectorCredentialPairStatus
    
    with get_session_with_current_tenant() as db_session:
        # 查询所有连接器
        cc_pairs = db_session.query(ConnectorCredentialPair).join(
            Connector, ConnectorCredentialPair.connector_id == Connector.id
        ).all()
        
        print('连接器配置:')
        print('ID\\t连接器名称\\t\\t状态\\t\\t\\t源类型')
        print('-' * 70)
        for cc_pair in cc_pairs:
            connector_name = cc_pair.connector.name[:15]
            status = cc_pair.status.value
            source_type = cc_pair.connector.source.value
            print(f'{cc_pair.id}\\t{connector_name}\\t{status}\\t{source_type}')
        
        # 统计状态
        active_count = len([cc for cc in cc_pairs if cc.status == ConnectorCredentialPairStatus.ACTIVE])
        paused_count = len([cc for cc in cc_pairs if cc.status == ConnectorCredentialPairStatus.PAUSED])
        
        print(f'\\n连接器状态统计:')
        print(f'活跃: {active_count}')
        print(f'暂停: {paused_count}')
        print(f'总计: {len(cc_pairs)}')
        
except Exception as e:
    print(f'连接器配置查询失败: {e}')
    import traceback
    traceback.print_exc()
" || log_error "连接器配置查询失败"

# 6. 检查关键日志
echo ""
log_info "步骤6: 检查关键日志"
echo "----------------------------------------"

log_info "检查primary worker日志..."
if docker exec km-background test -f /var/log/celery_worker_primary.log; then
    echo "Primary Worker最近日志（包含check_for_indexing）:"
    docker exec km-background tail -30 /var/log/celery_worker_primary.log | grep -E "(check_for_indexing|indexing|task|error)" || echo "未找到相关日志"
fi

log_info "检查docfetching worker日志..."
if docker exec km-background test -f /var/log/celery_worker_docfetching.log; then
    echo "Docfetching Worker最近日志:"
    docker exec km-background tail -20 /var/log/celery_worker_docfetching.log | grep -E "(docfetching|task|error)" || echo "未找到相关日志"
fi

log_info "检查beat调度日志..."
if docker exec km-background test -f /var/log/celery_beat.log; then
    echo "Celery Beat最近日志:"
    docker exec km-background tail -10 /var/log/celery_beat.log | grep -E "(check_for_indexing|beat|schedule)" || echo "未找到相关日志"
fi

# 7. 生成诊断报告
echo ""
log_info "步骤7: 生成诊断报告"
echo "----------------------------------------"

echo ""
echo "📋 索引任务诊断报告:"
echo "1. Supervisord: ✅ 所有worker正常运行"
echo "2. Celery连接: $(docker exec km-background timeout 5 celery -A onyx.background.celery.versioned_apps.primary inspect ping 2>/dev/null | grep -q pong && echo '✅ 正常' || echo '❌ 异常')"
echo "3. 数据库连接: $(docker exec km-background python3 -c 'from onyx.db.engine.sql_engine import SqlEngine; from onyx.configs.app_configs import POSTGRES_HOST, POSTGRES_PORT, POSTGRES_USER, POSTGRES_PASSWORD, POSTGRES_DB; SqlEngine.init_engine(f\"postgresql://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}\"); print(\"OK\")' 2>/dev/null && echo '✅ 正常' || echo '❌ 异常')"

echo ""
echo "🔧 下一步建议:"
echo "1. 如果有NOT_STARTED任务但队列为空，问题在任务创建逻辑"
echo "2. 如果队列有任务但未处理，问题在worker执行"
echo "3. 如果连接器为暂停状态，需要激活连接器"
echo "4. 检查连接器的刷新频率和触发条件"

echo ""
echo "📱 有用的命令:"
echo "# 查看实时日志"
echo "docker logs km-background -f | grep -E '(indexing|docfetching|check_for_indexing)'"
echo ""
echo "# 手动触发特定连接器索引"
echo "# (需要在Web界面或API中操作)"
