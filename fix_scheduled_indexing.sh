#!/bin/bash

set -e

echo "🔧 修复Indexing Attempts处于Scheduled状态问题"
echo "=============================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 1. 备份当前状态
echo ""
log_info "步骤1: 备份当前索引任务状态"
echo "----------------------------------------"

log_info "导出当前索引任务状态到备份文件..."
docker exec km-background python3 -c "
import os
import sys
import json
from datetime import datetime
sys.path.append('/app')

from onyx.db.engine.sql_engine import get_session_with_current_tenant
from onyx.db.models import IndexAttempt
from sqlalchemy import desc

try:
    with get_session_with_current_tenant() as db_session:
        attempts = db_session.query(IndexAttempt).order_by(desc(IndexAttempt.time_created)).limit(50).all()
        
        backup_data = []
        for attempt in attempts:
            backup_data.append({
                'id': attempt.id,
                'status': attempt.status.value,
                'connector_credential_pair_id': attempt.connector_credential_pair_id,
                'time_created': attempt.time_created.isoformat() if attempt.time_created else None,
                'time_started': attempt.time_started.isoformat() if attempt.time_started else None,
                'celery_task_id': attempt.celery_task_id
            })
        
        backup_file = f'/tmp/index_attempts_backup_{datetime.now().strftime(\"%Y%m%d_%H%M%S\")}.json'
        with open(backup_file, 'w') as f:
            json.dump(backup_data, f, indent=2)
        
        print(f'备份完成: {backup_file}')
        print(f'备份了 {len(backup_data)} 个索引任务记录')
        
except Exception as e:
    print(f'备份失败: {e}')
" || log_error "备份失败"

# 2. 重启相关服务
echo ""
log_info "步骤2: 重启相关服务"
echo "----------------------------------------"

log_info "重启Redis服务..."
docker compose -f docker-compose.main.yml restart redis

log_info "等待Redis启动..."
sleep 10

log_info "重启background服务..."
docker compose -f docker-compose.main.yml restart background

log_info "等待background服务启动..."
sleep 30

# 验证服务状态
log_info "验证服务状态..."
if docker ps | grep -q km-background && docker ps | grep -q redis; then
    log_success "服务重启成功"
else
    log_error "服务重启失败"
    exit 1
fi

# 3. 清理卡住的任务
echo ""
log_info "步骤3: 清理卡住的索引任务"
echo "----------------------------------------"

log_warning "即将清理处于NOT_STARTED状态超过1小时的索引任务"
read -p "确认继续？(y/N): " confirm
if [[ ! $confirm =~ ^[Yy]$ ]]; then
    log_info "跳过任务清理"
else
    log_info "清理卡住的索引任务..."
    docker exec km-background python3 -c "
import os
import sys
from datetime import datetime, timedelta
sys.path.append('/app')

from onyx.db.engine.sql_engine import get_session_with_current_tenant
from onyx.db.models import IndexAttempt
from onyx.db.enums import IndexingStatus
from sqlalchemy import and_

try:
    with get_session_with_current_tenant() as db_session:
        # 查找超过1小时的NOT_STARTED任务
        cutoff_time = datetime.utcnow() - timedelta(hours=1)
        
        stuck_attempts = db_session.query(IndexAttempt).filter(
            and_(
                IndexAttempt.status == IndexingStatus.NOT_STARTED,
                IndexAttempt.time_created < cutoff_time
            )
        ).all()
        
        if stuck_attempts:
            print(f'找到 {len(stuck_attempts)} 个卡住的任务')
            
            for attempt in stuck_attempts:
                print(f'取消任务 {attempt.id} (创建时间: {attempt.time_created})')
                attempt.status = IndexingStatus.CANCELED
                attempt.error_msg = '自动清理：任务长时间处于NOT_STARTED状态'
            
            db_session.commit()
            print(f'已取消 {len(stuck_attempts)} 个卡住的任务')
        else:
            print('没有找到卡住的任务')
            
except Exception as e:
    print(f'清理任务失败: {e}')
" || log_error "任务清理失败"
fi

# 4. 手动触发索引检查
echo ""
log_info "步骤4: 手动触发索引检查"
echo "----------------------------------------"

log_info "手动触发check_for_indexing任务..."
docker exec km-background python3 -c "
import os
import sys
sys.path.append('/app')

try:
    from celery import Celery
    from onyx.configs.app_configs import REDIS_HOST, REDIS_PORT, REDIS_DB_NUMBER_CELERY
    from onyx.configs.constants import OnyxCeleryTask, OnyxCeleryQueues, OnyxCeleryPriority
    
    # 创建Celery应用
    broker_url = f'redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB_NUMBER_CELERY}'
    app = Celery('onyx', broker=broker_url)
    
    # 发送check_for_indexing任务
    result = app.send_task(
        OnyxCeleryTask.CHECK_FOR_INDEXING,
        kwargs={'tenant_id': 'default'},
        queue=OnyxCeleryQueues.CELERY_PRIMARY,
        priority=OnyxCeleryPriority.HIGH
    )
    
    print(f'已发送check_for_indexing任务: {result.id}')
    
except Exception as e:
    print(f'发送任务失败: {e}')
" || log_warning "手动触发任务失败"

# 5. 验证修复结果
echo ""
log_info "步骤5: 验证修复结果"
echo "----------------------------------------"

sleep 60  # 等待任务处理

log_info "检查修复后的状态..."
docker exec km-background python3 -c "
import os
import sys
sys.path.append('/app')

from onyx.db.engine.sql_engine import get_session_with_current_tenant
from onyx.db.models import IndexAttempt
from onyx.db.enums import IndexingStatus
from sqlalchemy import desc, func

try:
    with get_session_with_current_tenant() as db_session:
        # 检查最近的任务状态
        recent_attempts = db_session.query(IndexAttempt).order_by(
            desc(IndexAttempt.time_created)
        ).limit(5).all()
        
        print('最近的索引任务状态:')
        print('ID\\t状态\\t\\t创建时间\\t\\t开始时间')
        print('-' * 60)
        for attempt in recent_attempts:
            created = attempt.time_created.strftime('%m-%d %H:%M') if attempt.time_created else 'None'
            started = attempt.time_started.strftime('%m-%d %H:%M') if attempt.time_started else 'None'
            print(f'{attempt.id}\\t{attempt.status.value}\\t{created}\\t{started}')
        
        # 统计当前状态
        status_counts = db_session.query(
            IndexAttempt.status, func.count(IndexAttempt.id)
        ).filter(
            IndexAttempt.time_created >= func.current_date()
        ).group_by(IndexAttempt.status).all()
        
        print('\\n今日任务状态统计:')
        for status, count in status_counts:
            print(f'{status.value}: {count}')
            
        # 检查是否还有NOT_STARTED状态的任务
        not_started_count = db_session.query(IndexAttempt).filter(
            IndexAttempt.status == IndexingStatus.NOT_STARTED
        ).count()
        
        if not_started_count > 0:
            print(f'\\n⚠️  仍有 {not_started_count} 个NOT_STARTED状态的任务')
        else:
            print('\\n✅ 没有卡住的NOT_STARTED任务')
            
except Exception as e:
    print(f'状态检查失败: {e}')
" || log_error "状态检查失败"

# 6. 检查Celery worker状态
echo ""
log_info "检查Celery worker状态..."
docker exec km-background python3 -c "
import os
import sys
sys.path.append('/app')

try:
    from celery import Celery
    from onyx.configs.app_configs import REDIS_HOST, REDIS_PORT, REDIS_DB_NUMBER_CELERY
    
    broker_url = f'redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB_NUMBER_CELERY}'
    app = Celery('onyx', broker=broker_url)
    
    inspect = app.control.inspect()
    
    # 检查worker状态
    stats = inspect.stats()
    if stats:
        print('Celery worker状态:')
        for worker, stat in stats.items():
            print(f'Worker: {worker}')
            print(f'  - 总任务数: {stat.get(\"total\", 0)}')
            print(f'  - 活跃任务: {len(inspect.active().get(worker, []))}')
    else:
        print('❌ 没有活跃的Celery worker')
        
except Exception as e:
    print(f'Worker检查失败: {e}')
" || log_warning "Worker状态检查失败"

# 7. 生成修复报告
echo ""
log_info "步骤6: 生成修复报告"
echo "----------------------------------------"

echo ""
log_success "修复操作完成！"
echo ""
echo "📋 修复总结:"
echo "1. ✅ 备份了当前索引任务状态"
echo "2. ✅ 重启了Redis和background服务"
echo "3. ✅ 清理了卡住的索引任务"
echo "4. ✅ 手动触发了索引检查任务"
echo "5. ✅ 验证了修复结果"

echo ""
echo "🔍 后续监控建议:"
echo "1. 观察页面上的Indexing Attempts状态变化"
echo "2. 检查background服务日志: docker logs km-background -f"
echo "3. 如果问题仍然存在，检查连接器配置和权限"

echo ""
echo "📱 有用的监控命令:"
echo "# 实时查看background日志"
echo "docker logs km-background -f | grep -i indexing"
echo ""
echo "# 检查当前索引任务状态"
echo "./diagnose_indexing_scheduled.sh"
echo ""
echo "# 查看Celery任务队列"
echo "docker exec km-background celery -A onyx.background.celery.celery_app inspect active"
