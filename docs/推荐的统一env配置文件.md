# 推荐的统一.env.main配置文件

## 完整配置内容

```bash
# ========== 基础服务配置 ==========

# 数据库配置（连接到远程数据库服务器）
POSTGRES_HOST=**********
POSTGRES_PORT=5432
POSTGRES_USER=km_user
POSTGRES_PASSWORD=Sygy@2025
POSTGRES_DB=knowledge-manage

# Redis配置（连接到数据库服务器）
REDIS_HOST=**********
REDIS_PORT=6379
REDIS_PASSWORD=

# Vespa搜索引擎配置
VESPA_HOST=**********
VESPA_PORT=8081
VESPA_TENANT_PORT=19071

# MinIO对象存储配置（连接到数据库服务器）
S3_ENDPOINT_URL=http://**********:9001
S3_AWS_ACCESS_KEY_ID=minioadmin
S3_AWS_SECRET_ACCESS_KEY=minioadmin

# ========== 内网大模型配置（标准化） ==========

# 推理模型配置（对话生成）- qwen3:32b
GEN_AI_PROVIDER=openai
GEN_AI_API_BASE=http://***********:9997/qwen3/v1
GEN_AI_API_KEY=dummy-key
GEN_AI_MODEL_NAME=qwen3:32b
GEN_AI_MAX_TOKENS=32768
GEN_AI_TEMPERATURE=0.7

# 重排模型配置（搜索优化）- qwen3-reranker-8b
RERANK_PROVIDER=openai
RERANK_API_BASE=http://***********:8000/v1
RERANK_API_KEY=dummy-key
RERANK_MODEL_NAME=qwen3-reranker-8b
RERANK_MAX_CONTEXT=32768

# 嵌入模型配置（向量化）- bge-large-zh-v1.5
EMBEDDING_PROVIDER=custom
EMBEDDING_API_BASE=http://***********:9997/v1
EMBEDDING_API_KEY=dummy-key
EMBEDDING_MODEL_NAME=bge-large-zh-v1.5
EMBEDDING_MAX_CONTEXT=3072
EMBEDDING_DIMENSION=1024

# ========== 兼容性配置（保持向后兼容） ==========

# 兼容旧版本的配置项
GEN_AI_API_ENDPOINT=http://***********:9997/qwen3/v1/chat/completions
EMBEDDING_API_ENDPOINT=http://***********:9997/v1/embeddings
INFERENCE_API_ENDPOINT=http://***********:9997/qwen3/v1/chat/completions

# 兼容旧版本的主机端口配置
EMBEDDING_MODEL_SERVER_HOST=***********
EMBEDDING_MODEL_SERVER_PORT=9997
INFERENCE_MODEL_SERVER_HOST=***********
INFERENCE_MODEL_SERVER_PORT=9997

# ========== Web服务配置 ==========

WEB_DOMAIN=http://**********:3000
CORS_ALLOWED_ORIGIN=http://**********
NEXT_PUBLIC_API_URL=http://**********:8080
NEXT_PUBLIC_DISABLE_STREAMING=false

# ========== 认证配置 ==========

AUTH_TYPE=disabled
SECRET=your-secret-key-here-change-in-production
ENCRYPTION_KEY_SECRET=your-encryption-key-here-change-in-production

# ========== 日志配置 ==========

LOG_LEVEL=info
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1

# ========== 模型缓存配置 ==========

MODEL_CACHE_DIR=/app/model_cache
HF_HOME=/app/model_cache
TRANSFORMERS_CACHE=/app/model_cache

# ========== Celery配置 ==========

CELERY_BROKER_URL=redis://**********:6379/0
CELERY_RESULT_BACKEND=redis://**********:6379/0

# ========== 性能优化配置 ==========

# 模型服务超时配置
MODEL_REQUEST_TIMEOUT=300
MODEL_CONNECTION_TIMEOUT=30
MODEL_READ_TIMEOUT=300

# 并发控制
MAX_CONCURRENT_REQUESTS=10
MAX_QUEUE_SIZE=100

# 缓存配置
ENABLE_MODEL_CACHE=true
MODEL_CACHE_TTL=3600
MODEL_CACHE_SIZE=1000
```

## 配置说明

### 🎯 统一配置的优势

1. **完整性**：包含所有三种模型的配置
2. **标准化**：使用统一的API Base格式
3. **兼容性**：保留旧版本配置项，确保向后兼容
4. **扩展性**：添加性能优化和缓存配置
5. **清晰性**：按功能模块分组，便于维护

### 🔧 关键改进

1. **新增重排模型**：完整的qwen3-reranker-8b配置
2. **API格式统一**：使用API_BASE + 具体端点的标准格式
3. **参数完整**：添加max_tokens、temperature等重要参数
4. **性能优化**：添加超时、并发、缓存等配置
5. **向后兼容**：保留原有配置项，确保系统正常运行

### ⚠️ 使用建议

1. **替换现有配置**：建议使用这个统一配置替换现有的.env.main
2. **测试验证**：替换后需要重启服务并进行功能测试
3. **生产环境**：记得修改密码和密钥配置
4. **监控调优**：根据实际使用情况调整性能参数
