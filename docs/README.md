# Onyx 文档中心

## 📚 文档索引

### 🚀 安装和部署
- [快速启动指南](quick-start-guide.md) - 快速启动Onyx系统
- [安装成功报告](installation-success-report.md) - 完整的安装验证报告

### 🔧 技术文档
- [技术架构报告](技术架构报告.md) - Onyx系统的技术架构分析
- [CE与EE功能对比分析](CE与EE功能对比分析.md) - 社区版与企业版功能对比

### 📦 依赖管理
- [后端依赖总结](backend-dependencies-summary.md) - 后端156个依赖包的详细说明
- [后端依赖指南](backend-dependency-guide.md) - 后端依赖安装指南
- [前端依赖指南](frontend-dependency-guide.md) - 前端依赖安装指南

## 🎯 文档分类

### 📖 用户指南
适合最终用户和系统管理员：
- [快速启动指南](quick-start-guide.md)

### 🔧 开发者文档
适合开发者和技术人员：
- [技术架构报告](技术架构报告.md)
- [后端依赖总结](backend-dependencies-summary.md)
- [CE与EE功能对比分析](CE与EE功能对比分析.md)

### 📋 安装报告
记录安装过程和结果：
- [安装成功报告](installation-success-report.md)
- [后端依赖指南](backend-dependency-guide.md)
- [前端依赖指南](frontend-dependency-guide.md)

## 🔍 快速查找

### 遇到安装问题？
2. 参考 [后端依赖总结](backend-dependencies-summary.md)
3. 运行测试脚本验证环境

### 想了解系统架构？
1. 阅读 [技术架构报告](技术架构报告.md)
2. 查看 [CE与EE功能对比分析](CE与EE功能对比分析.md)

### 需要快速启动？
1. 参考 [快速启动指南](quick-start-guide.md)
2. 查看 [安装成功报告](installation-success-report.md)

## 📊 文档统计

- **总文档数**: 8个
- **安装指南**: 3个
- **技术文档**: 2个
- **依赖文档**: 3个
- **最后更新**: 2025年1月9日

## 🔄 文档维护

### 更新频率
- **安装指南**: 随版本更新
- **技术文档**: 随架构变更
- **依赖文档**: 随依赖变更

### 贡献指南
1. 所有文档使用Markdown格式
2. 文件名使用中文或英文，保持一致性
3. 包含适当的emoji图标提高可读性
4. 提供清晰的代码示例和命令

## 📞 支持

如果文档中有不清楚的地方，请：
1. 检查相关的测试脚本
2. 查看安装日志
3. 参考GitHub Issues

---

**最后更新**: 2025年1月9日  
**维护者**: Onyx开发团队
