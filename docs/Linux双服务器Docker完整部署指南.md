# KM知识管理系统 - Linux双服务器Docker完整部署指南

## 📋 部署概述

本指南提供**傻瓜式**的完整部署流程，适用于在两台Linux服务器上部署KM知识管理系统。PostgreSQL安装在宿主机，其他服务使用Docker容器化部署。

### 🏗️ 服务器架构

| 服务器 | IP地址 | 配置 | 主要服务 |
|--------|--------|------|----------|
| 主服务器 | ********** | CPU:8核, 内存:32G, 磁盘:100G | 应用服务、搜索引擎、AI模型 |
| 数据库服务器 | ********** | CPU:8核, 内存:32G, 磁盘:100G | PostgreSQL、Redis、MinIO |

### 📦 服务分布详情

#### 主服务器 (**********) 服务清单
| 服务名称 | 容器名 | 端口 | 说明 |
|----------|--------|------|------|
| Nginx反向代理 | km-nginx | 80, 443 | 前端入口，负载均衡 |
| Web前端 | km-web-server | 3000 | Next.js前端应用 |
| API后端 | km-api-server | 8080 | FastAPI后端服务 |
| AI模型服务器 | km-model-server | 9000 | 本地AI处理服务（意图识别、内容分析） |
| Vespa搜索引擎 | km-vespa | 8081, 19071 | 混合检索搜索引擎 |
| 后台任务处理 | km-background | - | Celery后台任务 |

**注意**: 系统采用混合AI架构，包含本地AI处理和远程大模型服务：

| 服务类型 | 模型/服务名称 | 服务地址 | 技术栈 | 用途 |
|----------|----------|----------|--------|------|
| **本地AI服务** | model_server | **********:9000 | FastAPI + PyTorch | 意图识别、内容分析、本地AI处理 |
| **远程推理模型** | qwen3:32b | ***********:9997 | vLLM + GPTQ-Int8 | 对话生成、问答推理 |
| **远程重排模型** | qwen3-reranker-8b | ***********:8000 | vLLM + float16 | 搜索结果重排序 |
| **远程嵌入模型** | bge-large-zh-v1.5 | ***********:9997 | Xinference | 文档向量化、语义搜索 |

#### 数据库服务器 (**********) 服务清单
| 服务名称 | 安装方式 | 端口 | 说明 |
|----------|----------|------|------|
| PostgreSQL数据库 | 宿主机直接安装 | 5432 | 主数据库，支持远程访问 |
| Redis缓存 | km-redis | 6379 | 缓存服务 |
| MinIO对象存储 | km-minio | 9001, 9002 | 文件存储服务 |

## 🚀 第一阶段：数据库服务器 (**********) 部署

### 步骤1：系统环境准备

#### 1.1 更新系统包
```bash
# 更新系统包管理器
sudo dnf update -y

# 安装基础工具
sudo dnf install -y \
    curl \
    wget \
    git \
    vim \
    net-tools \
    firewalld \
    dnf-utils \
    device-mapper-persistent-data \
    lvm2
```

#### 1.2 安装Docker和Docker Compose
```bash
# 添加Docker官方仓库
sudo dnf config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo

# 安装Docker CE
sudo dnf install -y docker-ce docker-ce-cli containerd.io

# 启动并启用Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 将当前用户添加到docker组
sudo usermod -aG docker $USER

# 安装Docker Compose
COMPOSE_VERSION="2.24.0"
sudo curl -L "https://github.com/docker/compose/releases/download/v${COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
sudo ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose

# 验证安装
docker --version
docker-compose --version

# 重新登录以使用户组生效
echo "请重新登录以使Docker用户组生效"
```

### 步骤2：PostgreSQL宿主机安装配置 （在**********服务器上执行安装）

#### 2.1 安装PostgreSQL 15
```bash
# 安装PostgreSQL 15
sudo dnf install -y postgresql15-server postgresql15-contrib

# 初始化数据库
sudo /usr/pgsql-15/bin/postgresql-15-setup initdb

# 启动并设置开机自启
sudo systemctl enable postgresql-15
sudo systemctl start postgresql-15

# 检查服务状态
sudo systemctl status postgresql-15
```

#### 2.2 配置PostgreSQL
```bash
# 编辑postgresql.conf配置文件
sudo vim /var/lib/pgsql/15/data/postgresql.conf
```

**在postgresql.conf中修改以下配置：**
```conf
# 网络连接配置
listen_addresses = '*'          # 监听所有IP地址
port = 5432                     # 默认端口

# 连接配置
max_connections = 200           # 最大连接数
shared_buffers = 256MB          # 共享缓冲区
effective_cache_size = 1GB      # 有效缓存大小

# 安全配置
ssl = off                       # 生产环境建议开启SSL
password_encryption = md5       # 密码加密方式

# 日志配置
log_statement = 'all'           # 记录所有SQL语句
log_min_duration_statement = 1000  # 记录慢查询
```

#### 2.3 配置访问控制
```bash
# 编辑pg_hba.conf访问控制文件
sudo vim /var/lib/pgsql/15/data/pg_hba.conf
```

**在pg_hba.conf中添加以下配置：**
```conf
# TYPE  DATABASE        USER            ADDRESS                 METHOD

# 本地连接
local   all             all                                     trust
host    all             all             127.0.0.1/32            md5
host    all             all             ::1/128                 md5

# 允许主服务器连接
host    all             km_user         **********/32           md5

# 允许内网连接
host    all             km_user         *********/24            md5

# 允许管理员远程连接
host    all             postgres        *********/24            md5
```

#### 2.4 创建数据库和用户
```bash
# 切换到postgres用户
sudo -u postgres psql

# 在PostgreSQL命令行中执行：
```

```sql
-- 创建数据库
CREATE DATABASE "knowledge-manage";

-- 创建用户（与管理员权限一致）
CREATE USER km_user WITH PASSWORD 'Sygy@2025';

-- 授予超级用户权限
ALTER USER km_user WITH SUPERUSER CREATEDB CREATEROLE REPLICATION;

-- 授权数据库
GRANT ALL PRIVILEGES ON DATABASE "knowledge-manage" TO km_user;

-- 连接到knowledge-manage数据库
\c "knowledge-manage";

-- 创建必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- 授权给km_user
GRANT ALL PRIVILEGES ON SCHEMA public TO km_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO km_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO km_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO km_user;

-- 退出
\q
```

#### 2.5 重启PostgreSQL服务
```bash
# 重启服务使配置生效
sudo systemctl restart postgresql-15

# 验证服务状态
sudo systemctl status postgresql-15

# 测试本地连接
sudo -u postgres psql -c "SELECT version();"

# 测试km_user连接
PGPASSWORD="Sygy@2025" psql -h localhost -U km_user -d "knowledge-manage" -c "SELECT 1;"
```

### 步骤3：配置防火墙 （**********和**********服务器防火墙都已经关闭，不考虑这一点）

### 步骤4：部署Redis和MinIO容器

#### 4.1 创建数据目录
```bash
# 创建数据存储目录
sudo mkdir -p /opt/km/data/{redis,minio}
sudo chown -R $USER:$USER /opt/km/data
```

#### 4.2 创建docker-compose.db.yml
```bash
# 创建配置目录
mkdir -p ~/km-deploy
cd ~/km-deploy

# 创建数据库服务器的docker-compose配置文件
cat > docker-compose.db.yml << 'EOF'
version: '3.8'

services:
  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: km-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --bind 0.0.0.0
    volumes:
      - /opt/km/data/redis:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - km_network

  # MinIO对象存储服务
  minio:
    image: minio/minio:RELEASE.2024-01-16T16-07-38Z
    container_name: km-minio
    restart: unless-stopped
    ports:
      - "9001:9000"
      - "9002:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - /opt/km/data/minio:/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - km_network

networks:
  km_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  redis_data:
  minio_data:
EOF
```

#### 4.3 启动Redis和MinIO服务 （在**********服务器上安装到docker容器中）
```bash
# 启动服务
docker-compose -f docker-compose.db.yml up -d

# 检查服务状态
docker-compose -f docker-compose.db.yml ps

# 查看日志
docker-compose -f docker-compose.db.yml logs

# 验证服务
docker exec km-redis redis-cli ping
curl -f http://localhost:9001
```

### 步骤5：验证数据库服务器部署
```bash
# 检查PostgreSQL状态
sudo systemctl status postgresql-15

# 检查容器状态
docker ps

# 测试从主服务器的连接（在主服务器上执行）
PGPASSWORD="Sygy@2025" psql -h ********** -U km_user -d "knowledge-manage" -c "SELECT 1;"

# 测试Redis连接
redis-cli -h ********** -p 6379 ping

# 测试MinIO连接
curl -f http://**********:9001

echo "✅ 数据库服务器部署完成！"
```

## 🚀 第二阶段：主服务器 (**********) 部署

### 步骤1：系统环境准备

#### 1.1 更新系统包和安装Docker
```bash
# 执行与数据库服务器相同的系统准备步骤
# （参考第一阶段步骤1.1和1.2）
```

### 步骤2：获取项目代码
```bash
# 克隆项目代码
git clone http://git.virtueit.net/air/knowledgemanageronyx.git knowledgemanager
cd knowledgemanager

# 查看所有分支
git branch -a

# 切换到dev分支（最新代码）
git checkout dev

# 验证当前分支
git branch
# 应该显示 * dev

# 拉取最新代码
git pull origin dev

# 设置执行权限
chmod +x deploy-openeuler-cluster.sh verify-cluster-deployment.sh

# 验证项目结构
ls -la
echo "✅ 项目代码获取完成，当前使用dev分支"
```

**重要说明：**
- 项目有两个分支：main、dev
- **dev分支包含最新的功能和修复**，建议用于部署
- 如果需要使用其他分支，可以执行：`git checkout <branch-name>`

#### 分支详细说明
```bash
# 查看所有分支（包括远程分支）
git branch -a

# 切换分支的完整流程
git checkout dev                    # 切换到dev分支
git pull origin dev                 # 拉取最新代码
git log --oneline -5               # 查看最近5次提交

# 如果遇到分支切换问题
git fetch origin                   # 获取最新的远程分支信息
git checkout -b dev origin/dev     # 创建并切换到dev分支

# 验证当前状态
git status                         # 查看工作区状态
git branch                         # 确认当前分支
```

**分支选择建议：**
- **dev分支**: 最新功能，推荐用于开发和测试环境
- **main分支**: 稳定版本，用于生产环境

### 步骤3：配置环境变量

#### 3.1 创建主服务器环境配置文件 （**********服务器上配置.env.main）
```bash
# 创建主服务器环境配置文件
cat > .env.main << 'EOF'
# 数据库配置（连接到远程数据库服务器）
POSTGRES_HOST=**********
POSTGRES_PORT=5432
POSTGRES_USER=km_user
POSTGRES_PASSWORD=Sygy@2025
POSTGRES_DB=knowledge-manage

# Redis配置（连接到数据库服务器）
REDIS_HOST=**********
REDIS_PORT=6379
REDIS_PASSWORD=

# Vespa搜索引擎配置
VESPA_HOST=**********
VESPA_PORT=8081
VESPA_TENANT_PORT=19071

# MinIO对象存储配置（连接到数据库服务器）
S3_ENDPOINT_URL=http://**********:9001
S3_AWS_ACCESS_KEY_ID=minioadmin
S3_AWS_SECRET_ACCESS_KEY=minioadmin

# ========== LLM模型配置 ==========
# 推理模型配置（对话生成）- qwen3:32b
GEN_AI_PROVIDER=openai
GEN_AI_API_BASE=http://***********:9997/qwen3/v1
GEN_AI_API_KEY=dummy-key
GEN_AI_MODEL_NAME=qwen3:32b
GEN_AI_MAX_TOKENS=32768
GEN_AI_TEMPERATURE=0.7

# 重排模型配置（搜索优化）- qwen3-reranker-8b
RERANK_PROVIDER=openai
RERANK_API_BASE=http://***********:8000/v1
RERANK_API_KEY=dummy-key
RERANK_MODEL_NAME=qwen3-reranker-8b
RERANK_MAX_CONTEXT=32768

# 嵌入模型配置（向量化）- bge-large-zh-v1.5
EMBEDDING_PROVIDER=custom
EMBEDDING_API_BASE=http://***********:9997/v1
EMBEDDING_API_KEY=dummy-key
EMBEDDING_MODEL_NAME=bge-large-zh-v1.5
EMBEDDING_MAX_CONTEXT=3072
EMBEDDING_DIMENSION=1024

# 模型服务性能配置
MODEL_REQUEST_TIMEOUT=300
MODEL_CONNECTION_TIMEOUT=30
MODEL_READ_TIMEOUT=300
MAX_CONCURRENT_REQUESTS=10

# 兼容性配置（保持向后兼容）
EMBEDDING_MODEL_SERVER_HOST=***********
EMBEDDING_MODEL_SERVER_PORT=9997
INFERENCE_MODEL_SERVER_HOST=***********
INFERENCE_MODEL_SERVER_PORT=9997

# Web服务配置
WEB_DOMAIN=http://**********:3000
CORS_ALLOWED_ORIGIN=http://**********
NEXT_PUBLIC_API_URL=http://**********:8080
NEXT_PUBLIC_DISABLE_STREAMING=false

# 认证配置
AUTH_TYPE=disabled
SECRET=your-secret-key-here-change-in-production
ENCRYPTION_KEY_SECRET=your-encryption-key-here-change-in-production

# 日志配置
LOG_LEVEL=info
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1

# 模型缓存配置
MODEL_CACHE_DIR=/app/model_cache
HF_HOME=/app/model_cache
TRANSFORMERS_CACHE=/app/model_cache

# Model Server配置（本地AI处理服务）
MODEL_SERVER_HOST=model_server
MODEL_SERVER_PORT=9000

# Celery配置
CELERY_BROKER_URL=redis://**********:6379/0
CELERY_RESULT_BACKEND=redis://**********:6379/0
EOF
```

#### 3.2 验证环境配置
```bash
# 检查配置文件
cat .env.main

# 测试**********服务器上的数据库连接
docker run --rm -e PGPASSWORD="Sygy@2025" postgres:13 psql -h ********** -U km_user -d "knowledge-manage" -c "SELECT 1;"

# 测试Redis连接
docker run --rm redis:6 redis-cli -h ********** -p 6379 ping
```

### 步骤4：创建主服务器Docker Compose配置

#### 4.1 创建数据目录
```bash
# 创建必要的数据目录
sudo mkdir -p /opt/km/{vespa,model_cache,logs,nginx}
sudo chown -R $USER:$USER /opt/km
```

#### 4.2 创建Nginx配置
```bash
# 创建Nginx配置目录
mkdir -p /opt/km/nginx/conf.d

# 创建Nginx配置文件
cat > /opt/km/nginx/conf.d/km.conf << 'EOF'
upstream web_frontend {
    server **********:3000;
}

upstream api_backend {
    server **********:8080;
}

server {
    listen 80;
    server_name _;
    client_max_body_size 100M;

    # 前端路由
    location / {
        proxy_pass http://web_frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # API路由
    location /api/ {
        proxy_pass http://api_backend/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF
```

#### 4.3 创建主服务器docker-compose配置 （在**********服务器上配置docker-compose.main.yml文件，所有容器都由这个yaml文件build和启动）
```bash
# 使用项目中的docker-compose配置作为基础，创建主服务器配置
cat > docker-compose.main.yml << 'EOF'
version: '3.8'

services:
  # Vespa搜索引擎
  vespa:
    image: vespaengine/vespa:8.526.15
    container_name: km-vespa
    restart: unless-stopped
    ports:
      - "8081:8080"
      - "19071:19071"
    volumes:
      - vespa_data:/opt/vespa/var
    environment:
      - VESPA_CONFIGSERVERS=km-vespa  # ✅ 修复
      - VESPA_SKIP_UPGRADE_CHECK=true
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/ApplicationStatus"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s
    deploy:  # ✅ 添加资源限制
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 2G
    logging:  # ✅ 添加日志配置
      driver: json-file
      options:
        max-size: "50m"
        max-file: "6"
    networks:
      - km_network

  # AI模型服务器（本地处理）
  model_server:
    build:
      context: .
      dockerfile: docker/Dockerfile.model-server
    container_name: km-model-server
    restart: unless-stopped
    ports:
      - "9000:9000"
    environment:
      - REDIS_URL=redis://**********:6379/1
      - MODEL_CACHE_DIR=/app/model_cache
      - PYTHONPATH=/app
      - MODEL_SERVER_HOST=0.0.0.0
      - MODEL_SERVER_PORT=9000
      - INDEXING_ONLY=false
    volumes:
      - model_cache:/app/model_cache
      - /opt/km/logs:/app/logs
    depends_on:
      - dependency_check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 1G
    security_opt:
      - no-new-privileges:true
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "6"
    networks:
      - km_network

  # API后端服务
  api_server:
    build:
      context: .
      dockerfile: docker/Dockerfile.backend
    container_name: km-api-server
    restart: unless-stopped
    ports:
      - "8080:8080"
    env_file:
      - .env.main
    depends_on:
      vespa:
        condition: service_healthy
      model_server:
        condition: service_healthy
    volumes:
      - /opt/km/logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:  # ✅ 添加资源限制
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G
    security_opt:  # ✅ 添加安全配置
      - no-new-privileges:true
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "6"
    networks:
      - km_network

  # 后台任务处理
  background:
    build:
      context: .  # ✅ 修复：使用项目根目录作为构建上下文
      dockerfile: docker/Dockerfile.worker
    container_name: km-background
    restart: unless-stopped
    env_file:
      - .env.main
    depends_on:
      api_server:
        condition: service_healthy
    volumes:
      - background_logs:/app/logs
      - model_cache:/app/model_cache
    deploy:  # ✅ 添加资源限制
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.25'
          memory: 512M
    security_opt:
      - no-new-privileges:true
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "6"
    networks:
      - km_network

  # Web前端服务
  web_server:
    build:
      context: .  # ✅ 修复：使用项目根目录作为构建上下文
      dockerfile: docker/Dockerfile.web
    container_name: km-web-server
    restart: unless-stopped
    ports:
      - "3000:3000"
    env_file:
      - .env.main
    depends_on:
      api_server:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]  # ✅ 更好的健康检查
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 256M
    security_opt:
      - no-new-privileges:true
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "6"
    networks:
      - km_network

  # Nginx反向代理
  nginx:
    image: nginx:1.25-alpine  # ✅ 固定版本
    container_name: km-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - nginx_config:/etc/nginx/conf.d:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      web_server:
        condition: service_healthy
      api_server:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 64M
    security_opt:
      - no-new-privileges:true
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "6"
    networks:
      - km_network

  # ✅ 添加外部依赖检查服务
  dependency_check:
    image: alpine:latest
    container_name: km-dependency-check
    command: >
      sh -c "
        apk add --no-cache curl postgresql-client redis &&
        echo 'Checking external dependencies...' &&
        until pg_isready -h ********** -p 5432 -U km_user; do
          echo 'Waiting for PostgreSQL...'; sleep 5;
        done &&
        until redis-cli -h ********** -p 6379 ping; do
          echo 'Waiting for Redis...'; sleep 5;
        done &&
        until curl -f http://**********:9001; do
          echo 'Waiting for MinIO...'; sleep 5;
        done &&
        echo 'All external dependencies are ready!'
      "
    networks:
      - km_network

# ✅ 定义数据卷
volumes:
  vespa_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/km/vespa
  api_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/km/logs/api
  background_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/km/logs/background
  model_cache:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/km/model_cache
  nginx_config:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/km/nginx/conf.d
  nginx_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/km/logs/nginx

networks:
  km_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
EOF
```

### 步骤5：配置防火墙 （防火墙均已经关闭，不考虑这一点）

### 步骤6：部署主服务器服务 （在**********服务器上执行）

#### 6.1 构建Docker镜像
```bash
# 构建所有镜像（这可能需要较长时间）
echo "🏗️ 开始构建Docker镜像..."
docker compose -f docker-compose.main.yml build --no-cache

# 检查构建的镜像
docker images | grep km
```

#### 6.2 按顺序启动服务
```bash
# 第一步：启动Vespa搜索引擎
echo "📊 启动Vespa搜索引擎..."
docker compose -f docker-compose.main.yml up -d vespa

# 等待Vespa启动完成（重要：Vespa需要较长启动时间）
echo "⏳ 等待Vespa启动完成（约2-3分钟）..."
timeout=180
elapsed=0
while ! curl -f http://localhost:8081/ApplicationStatus &>/dev/null; do
    sleep 10
    elapsed=$((elapsed + 10))
    echo "等待中... ${elapsed}/${timeout}秒"
    if [[ $elapsed -ge $timeout ]]; then
        echo "❌ Vespa启动超时，请检查日志"
        docker compose -f docker-compose.main.yml logs vespa
        exit 1
    fi
done
echo "✅ Vespa搜索引擎启动成功"

# 第二步：启动AI模型服务器
echo "🤖 启动AI模型服务器..."
docker compose -f docker-compose.main.yml up -d model_server

# 等待model_server启动
echo "⏳ 等待AI模型服务器启动..."
timeout=120
elapsed=0
while ! curl -f http://localhost:9000/health &>/dev/null; do
    sleep 5
    elapsed=$((elapsed + 5))
    echo "等待中... ${elapsed}/${timeout}秒"
    if [[ $elapsed -ge $timeout ]]; then
        echo "❌ AI模型服务器启动超时，请检查日志"
        docker compose -f docker-compose.main.yml logs model_server
        exit 1
    fi
done
echo "✅ AI模型服务器启动成功"

# 第三步：验证外部AI模型服务连接
echo "🧠 验证外部AI模型服务连接..."

# 检查对话模型服务
if curl -f http://***********:9997/qwen3/v1/models &>/dev/null; then
    echo "✅ 对话模型服务 (qwen3:32b) 连接正常"
else
    echo "⚠️ 对话模型服务连接失败，请检查 ***********:9997"
fi

# 检查嵌入模型服务
if curl -f http://***********:9997/v1/models &>/dev/null; then
    echo "✅ 嵌入模型服务 (bge-large-zh-v1.5) 连接正常"
else
    echo "⚠️ 嵌入模型服务连接失败，请检查 ***********:9997"
fi

# 第四步：启动API后端服务
echo "⚙️ 启动API后端服务..."
docker compose -f docker-compose.main.yml up -d api_server

# 等待API服务启动
echo "⏳ 等待API服务启动..."
timeout=120
elapsed=0
while ! curl -f http://localhost:8080/health &>/dev/null; do
    sleep 5
    elapsed=$((elapsed + 5))
    echo "等待中... ${elapsed}/${timeout}秒"
    if [[ $elapsed -ge $timeout ]]; then
        echo "❌ API服务启动超时，请检查日志"
        docker compose -f docker-compose.main.yml logs api_server
        exit 1
    fi
done
echo "✅ API后端服务启动成功"

# 第五步：执行数据库迁移
echo "🗄️ 执行数据库迁移..."
docker exec km-api-server alembic upgrade head

# 第五步：启动后台任务处理
echo "🔄 启动后台任务处理..."
docker compose -f docker-compose.main.yml up -d background

# 第六步：启动Web前端服务
echo "🌐 启动Web前端服务..."
docker compose -f docker-compose.main.yml up -d web_server

# 等待前端服务启动
echo "⏳ 等待前端服务启动..."
timeout=60
elapsed=0
while ! curl -f http://localhost:3000 &>/dev/null; do
    sleep 5
    elapsed=$((elapsed + 5))
    echo "等待中... ${elapsed}/${timeout}秒"
    if [[ $elapsed -ge $timeout ]]; then
        echo "❌ 前端服务启动超时，请检查日志"
        docker compose -f docker-compose.main.yml logs web_server
        exit 1
    fi
done
echo "✅ Web前端服务启动成功"

# 第七步：启动Nginx反向代理
echo "🔀 启动Nginx反向代理..."
docker compose -f docker-compose.main.yml up -d nginx

# 等待Nginx启动
sleep 10

echo "🎉 所有服务启动完成！"
```

#### 6.3 验证服务状态
```bash
# 检查所有容器状态
echo "📋 检查容器状态..."
docker compose -f docker-compose.main.yml ps

# 检查服务健康状态
echo "🔍 验证服务健康状态..."

# 检查Vespa搜索引擎
echo "检查Vespa搜索引擎..."
curl -f http://localhost:8081/ApplicationStatus && echo "✅ Vespa正常" || echo "❌ Vespa异常"

# 检查内网大模型服务连接
echo "检查内网大模型服务连接..."
curl -f http://***********:9997/qwen3/v1/models && echo "✅ 推理模型服务(qwen3:32b)正常" || echo "❌ 推理模型服务异常"
curl -f http://***********:8000/v1/models && echo "✅ 重排模型服务(qwen3-reranker-8b)正常" || echo "❌ 重排模型服务异常"
curl -f http://***********:9997/v1/models && echo "✅ 嵌入模型服务(bge-large-zh-v1.5)正常" || echo "❌ 嵌入模型服务异常"

# 检查API后端服务
echo "检查API后端服务..."
curl -f http://localhost:8080/health && echo "✅ API后端服务正常" || echo "❌ API后端服务异常"

# 检查Web前端服务
echo "检查Web前端服务..."
curl -f http://localhost:3000 && echo "✅ Web前端服务正常" || echo "❌ Web前端服务异常"

# 检查Nginx代理
echo "检查Nginx代理..."
curl -f http://localhost/health && echo "✅ Nginx代理正常" || echo "❌ Nginx代理异常"

# 检查数据库连接
echo "检查数据库连接..."
docker run --rm -e PGPASSWORD="Sygy@2025" postgres:13 psql -h ********** -U km_user -d "knowledge-manage" -c "SELECT 1;" && echo "✅ 数据库连接正常" || echo "❌ 数据库连接异常"

# 检查Redis连接
echo "检查Redis连接..."
docker run --rm redis:6 redis-cli -h ********** -p 6379 ping && echo "✅ Redis连接正常" || echo "❌ Redis连接异常"

# 检查MinIO连接
echo "检查MinIO连接..."
curl -f http://**********:9001 && echo "✅ MinIO连接正常" || echo "❌ MinIO连接异常"

echo "🎯 服务验证完成！"
```

## 🤖 第三阶段：内网大模型配置 （这个模型在服务器上已检查，已确定无问题）

### 大模型服务架构说明

本系统支持配置内网部署的大模型服务，包括以下三种类型的模型：

| 模型类型 | 模型名称 | 服务地址 | 用途说明 |
|----------|----------|----------|----------|
| 推理模型 | qwen3:32b | http://***********:9997/qwen3/v1/ | 对话生成、问答推理 |
| 重排模型 | qwen3-reranker-8b | http://***********:8000/v1/ | 搜索结果重排序 |
| 嵌入模型 | bge-large-zh-v1.5 | http://***********:9997/v1/embeddings | 文档向量化、语义搜索 |

### 配置方式选择

系统提供两种大模型配置方式：

1. **Web UI配置（推荐）**：通过管理界面进行配置，操作简单直观
2. **环境变量配置**：通过配置文件设置，适合批量部署

### 方案一：通过Web UI配置（推荐）

#### 步骤1：访问管理界面
```bash
# 确保系统已完全启动
docker-compose -f docker-compose.main.yml ps

# 访问管理界面
echo "请在浏览器中访问: http://**********/admin/configuration/llm"
```

#### 步骤2：配置推理模型（对话生成）
1. 点击"Add Custom LLM Provider"
2. 填写以下信息：
   ```
   Display Name: 内网推理模型-Qwen3
   Provider Name: openai
   API Base: http://***********:9997/qwen3/v1
   API Key: dummy-key（如果不需要认证）
   Default Model: qwen3:32b
   Fast Model: qwen3:32b
   ```
3. 点击"Test Connection"验证连接
4. 保存配置并设为默认提供商

#### 步骤3：配置重排模型（搜索优化）
1. 再次点击"Add Custom LLM Provider"
2. 填写以下信息：
   ```
   Display Name: 内网重排模型-Qwen3-Reranker
   Provider Name: openai
   API Base: http://***********:8000/v1
   API Key: dummy-key
   Default Model: qwen3-reranker-8b
   ```
3. 在"Model Configurations"中添加：
   - Model Name: qwen3-reranker-8b
   - Context Length: 32768
4. 保存配置

#### 步骤4：配置嵌入模型（向量化）
1. 导航到"Embedding Model Configuration"
2. 填写以下信息：
   ```
   Provider: Custom
   Model Name: bge-large-zh-v1.5
   API Base: http://***********:9997/v1
   API Key: dummy-key
   Max Context Length: 3072
   ```
3. 测试连接并保存

#### 步骤5：验证模型配置
```bash
# 创建模型配置验证脚本
cat > verify_llm_config.sh << 'EOF'
#!/bin/bash

echo "🤖 验证大模型配置..."

# 测试推理模型连接
echo "1. 测试推理模型连接..."
curl -X POST http://***********:9997/qwen3/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "qwen3:32b",
    "messages": [{"role": "user", "content": "你好"}],
    "max_tokens": 100
  }' && echo "✅ 推理模型连接正常" || echo "❌ 推理模型连接异常"

# 测试重排模型连接
echo "2. 测试重排模型连接..."
curl -X POST http://***********:8000/v1/rerank \
  -H "Content-Type: application/json" \
  -d '{
    "model": "qwen3-reranker-8b",
    "query": "测试查询",
    "documents": ["文档1", "文档2"]
  }' && echo "✅ 重排模型连接正常" || echo "❌ 重排模型连接异常"

# 测试嵌入模型连接
echo "3. 测试嵌入模型连接..."
curl -X POST http://***********:9997/v1/embeddings \
  -H "Content-Type: application/json" \
  -d '{
    "model": "bge-large-zh-v1.5",
    "input": "测试文本"
  }' && echo "✅ 嵌入模型连接正常" || echo "❌ 嵌入模型连接异常"

echo "🎯 模型配置验证完成！"
EOF

chmod +x verify_llm_config.sh
./verify_llm_config.sh
```

### 方案二：通过环境变量配置

#### 步骤1：更新环境配置文件
```bash
# 备份当前配置
cp .env.main .env.main.backup

# 更新环境配置，添加大模型配置
cat >> .env.main << 'EOF'

# ========== 内网大模型配置 ==========

# 推理模型配置（对话生成）
GEN_AI_PROVIDER=openai
GEN_AI_API_BASE=http://***********:9997/qwen3/v1
GEN_AI_API_KEY=dummy-key
GEN_AI_MODEL_NAME=qwen3:32b
GEN_AI_MAX_TOKENS=32768
GEN_AI_TEMPERATURE=0.7

# 重排模型配置（搜索优化）
RERANK_PROVIDER=openai
RERANK_API_BASE=http://***********:8000/v1
RERANK_API_KEY=dummy-key
RERANK_MODEL_NAME=qwen3-reranker-8b
RERANK_MAX_CONTEXT=32768

# 嵌入模型配置（向量化）
EMBEDDING_PROVIDER=custom
EMBEDDING_API_BASE=http://***********:9997/v1
EMBEDDING_API_KEY=dummy-key
EMBEDDING_MODEL_NAME=bge-large-zh-v1.5
EMBEDDING_MAX_CONTEXT=3072
EMBEDDING_DIMENSION=1024

# 模型服务超时配置
MODEL_REQUEST_TIMEOUT=300
MODEL_CONNECTION_TIMEOUT=30
MODEL_READ_TIMEOUT=300

# 模型缓存配置
ENABLE_MODEL_CACHE=true
MODEL_CACHE_TTL=3600
MODEL_CACHE_SIZE=1000

EOF
```

#### 步骤2：重启相关服务
```bash
# 重启API服务以加载新配置
docker-compose -f docker-compose.main.yml restart api_server

# 重启后台任务服务
docker-compose -f docker-compose.main.yml restart background

# 等待服务启动
sleep 30

# 验证服务状态
docker-compose -f docker-compose.main.yml ps
```

### 高级配置选项

#### 模型负载均衡配置
```bash
# 如果有多个相同类型的模型服务，可以配置负载均衡
cat >> .env.main << 'EOF'

# 推理模型负载均衡
GEN_AI_ENDPOINTS=http://***********:9997/qwen3/v1,http://10.0.83.200:9997/qwen3/v1
GEN_AI_LOAD_BALANCE_STRATEGY=round_robin

# 嵌入模型负载均衡
EMBEDDING_ENDPOINTS=http://***********:9997/v1,http://10.0.83.201:9997/v1
EMBEDDING_LOAD_BALANCE_STRATEGY=least_connections

EOF
```

#### 模型性能优化配置
```bash
# 添加性能优化配置
cat >> .env.main << 'EOF'

# 并发控制
MAX_CONCURRENT_REQUESTS=10
MAX_QUEUE_SIZE=100

# 批处理配置
ENABLE_BATCH_PROCESSING=true
BATCH_SIZE=32
BATCH_TIMEOUT=5

# 缓存优化
ENABLE_RESPONSE_CACHE=true
CACHE_EXPIRE_TIME=1800

EOF
```

### 模型配置验证和测试

#### 创建综合测试脚本
```bash
cat > test_llm_integration.sh << 'EOF'
#!/bin/bash

echo "🧪 开始大模型集成测试..."

# 测试API服务是否能正确调用模型
echo "1. 测试API服务模型调用..."

# 测试对话功能
echo "测试对话功能..."
curl -X POST http://localhost:8080/api/chat/send-message \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer dummy-token" \
  -d '{
    "message": "你好，请介绍一下你自己",
    "chat_session_id": "test-session"
  }' && echo "✅ 对话功能正常" || echo "❌ 对话功能异常"

# 测试文档搜索功能
echo "测试文档搜索功能..."
curl -X POST http://localhost:8080/api/search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "测试搜索",
    "limit": 5
  }' && echo "✅ 搜索功能正常" || echo "❌ 搜索功能异常"

# 测试文档上传和向量化
echo "测试文档向量化功能..."
curl -X POST http://localhost:8080/api/documents/upload \
  -H "Content-Type: multipart/form-data" \
  -F "file=@test_document.txt" && echo "✅ 文档向量化正常" || echo "❌ 文档向量化异常"

echo "🎯 大模型集成测试完成！"
EOF

chmod +x test_llm_integration.sh
```

### 故障排除

#### 常见问题及解决方案

**1. 模型服务连接超时**
```bash
# 检查网络连通性
ping ***********
ping ***********

# 检查端口开放
nc -z *********** 9997
nc -z *********** 8000
nc -z *********** 9997

# 增加超时时间
echo "MODEL_REQUEST_TIMEOUT=600" >> .env.main
```

**2. 模型响应格式错误**
```bash
# 检查模型API格式兼容性
curl -X GET http://***********:9997/qwen3/v1/models
curl -X GET http://***********:8000/v1/models
curl -X GET http://***********:9997/v1/models

# 查看API服务日志
docker logs km-api-server -f | grep -i "model\|llm\|error"
```

**3. 嵌入模型向量维度不匹配**
```bash
# 检查模型实际输出维度
curl -X POST http://***********:9997/v1/embeddings \
  -H "Content-Type: application/json" \
  -d '{"model": "bge-large-zh-v1.5", "input": "test"}' | jq '.data[0].embedding | length'

# 更新配置中的维度设置
sed -i 's/EMBEDDING_DIMENSION=1024/EMBEDDING_DIMENSION=实际维度/' .env.main
```

### 性能监控

#### 创建模型性能监控脚本
```bash
cat > monitor_llm_performance.sh << 'EOF'
#!/bin/bash

echo "📊 大模型性能监控报告 - $(date)"
echo "=================================="

# 检查模型服务响应时间
echo "🚀 模型服务响应时间："

# 推理模型响应时间
start_time=$(date +%s%N)
curl -s http://***********:9997/qwen3/v1/models > /dev/null
end_time=$(date +%s%N)
response_time=$(( (end_time - start_time) / 1000000 ))
echo "推理模型: ${response_time}ms"

# 重排模型响应时间
start_time=$(date +%s%N)
curl -s http://***********:8000/v1/models > /dev/null
end_time=$(date +%s%N)
response_time=$(( (end_time - start_time) / 1000000 ))
echo "重排模型: ${response_time}ms"

# 嵌入模型响应时间
start_time=$(date +%s%N)
curl -s http://***********:9997/v1/models > /dev/null
end_time=$(date +%s%N)
response_time=$(( (end_time - start_time) / 1000000 ))
echo "嵌入模型: ${response_time}ms"

# 检查API服务模型调用统计
echo ""
echo "📈 API服务模型调用统计："
docker logs km-api-server --since=1h | grep -c "model_call" || echo "无调用记录"

echo ""
echo "🎯 性能监控完成"
EOF

chmod +x monitor_llm_performance.sh
```

## 🎯 第四阶段：系统验证和测试

### 完整系统验证
```bash
# 创建系统验证脚本
cat > verify_deployment.sh << 'EOF'
#!/bin/bash

echo "🔍 开始完整系统验证..."

# 检查所有容器状态
echo "1. 检查容器状态..."
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# 检查网络连通性
echo "2. 检查网络连通性..."
ping -c 3 ********** && echo "✅ 数据库服务器网络正常" || echo "❌ 数据库服务器网络异常"

# 检查数据库连接
echo "3. 检查数据库连接..."
PGPASSWORD="Sygy@2025" psql -h ********** -U km_user -d "knowledge-manage" -c "SELECT current_database(), current_user, version();" && echo "✅ 数据库连接正常" || echo "❌ 数据库连接异常"

# 检查Redis连接
echo "4. 检查Redis连接..."
redis-cli -h ********** -p 6379 info server | head -5 && echo "✅ Redis连接正常" || echo "❌ Redis连接异常"

# 检查MinIO连接
echo "5. 检查MinIO连接..."
curl -s http://**********:9001 | grep -q "MinIO" && echo "✅ MinIO连接正常" || echo "❌ MinIO连接异常"

# 检查API服务
echo "6. 检查API服务..."
curl -s http://localhost:8080/health | grep -q "ok" && echo "✅ API服务正常" || echo "❌ API服务异常"

# 检查前端服务
echo "7. 检查前端服务..."
curl -s http://localhost:3000 | grep -q "html" && echo "✅ 前端服务正常" || echo "❌ 前端服务异常"

# 检查Nginx代理
echo "8. 检查Nginx代理..."
curl -s http://localhost/health | grep -q "healthy" && echo "✅ Nginx代理正常" || echo "❌ Nginx代理异常"

# 检查Vespa搜索引擎
echo "9. 检查Vespa搜索引擎..."
curl -s http://localhost:8081/ApplicationStatus | grep -q "OK" && echo "✅ Vespa搜索引擎正常" || echo "❌ Vespa搜索引擎异常"

# 检查AI模型服务
echo "10. 检查AI模型服务..."
curl -s http://localhost:9000/health | grep -q "ok" && echo "✅ 推理模型服务正常" || echo "❌ 推理模型服务异常"
curl -s http://localhost:9001/health | grep -q "ok" && echo "✅ 索引模型服务正常" || echo "❌ 索引模型服务异常"

echo "🎉 系统验证完成！"
echo ""
echo "📱 访问地址："
echo "   主页面: http://**********"
echo "   API文档: http://**********/api/docs"
echo "   直接前端: http://**********:3000"
echo "   直接API: http://**********:8080"
echo "   MinIO控制台: http://**********:9002 (minioadmin/minioadmin)"
EOF

chmod +x verify_deployment.sh
./verify_deployment.sh
```

## 🔧 第四阶段：运维管理

### 服务管理命令

#### 启动所有服务
```bash
# 数据库服务器
cd ~/km-deploy
docker-compose -f docker-compose.db.yml up -d

# 主服务器
cd ~/km
docker-compose -f docker-compose.main.yml up -d
```

#### 停止所有服务
```bash
# 主服务器
docker-compose -f docker-compose.main.yml down

# 数据库服务器
docker-compose -f docker-compose.db.yml down
```

#### 重启特定服务
```bash
# 重启API服务
docker-compose -f docker-compose.main.yml restart api_server

# 重启前端服务
docker-compose -f docker-compose.main.yml restart web_server

# 重启Nginx代理
docker-compose -f docker-compose.main.yml restart nginx
```

#### 查看服务日志
```bash
# 查看所有服务日志
docker-compose -f docker-compose.main.yml logs

# 查看特定服务日志
docker-compose -f docker-compose.main.yml logs -f api_server
docker-compose -f docker-compose.main.yml logs -f web_server
docker-compose -f docker-compose.main.yml logs -f vespa

# 查看最近的日志
docker-compose -f docker-compose.main.yml logs --tail=100 api_server
```

### 数据库维护

#### PostgreSQL维护
```bash
# 连接数据库（在数据库服务器上）
PGPASSWORD="Sygy@2025" psql -h localhost -U km_user -d "knowledge-manage"

# 备份数据库
sudo -u postgres pg_dump "knowledge-manage" > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复数据库
sudo -u postgres psql "knowledge-manage" < backup.sql

# 查看数据库大小
PGPASSWORD="Sygy@2025" psql -h localhost -U km_user -d "knowledge-manage" -c "SELECT pg_size_pretty(pg_database_size('knowledge-manage'));"

# 查看连接数
PGPASSWORD="Sygy@2025" psql -h localhost -U km_user -d "knowledge-manage" -c "SELECT count(*) FROM pg_stat_activity;"

# 重启PostgreSQL服务
sudo systemctl restart postgresql-15
```

#### Redis维护
```bash
# 连接Redis
docker exec -it km-redis redis-cli

# 查看Redis信息
docker exec km-redis redis-cli info

# 清空Redis缓存（谨慎使用）
docker exec km-redis redis-cli flushall
```

### 系统监控

#### 资源监控脚本
```bash
cat > monitor_system.sh << 'EOF'
#!/bin/bash

echo "📊 系统资源监控报告 - $(date)"
echo "=================================="

# 系统资源
echo "💻 系统资源使用情况："
echo "CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
echo "内存使用: $(free -h | awk 'NR==2{printf "%.1f%%", $3*100/$2}')"
echo "磁盘使用: $(df -h / | awk 'NR==2{print $5}')"

# Docker容器状态
echo ""
echo "🐳 Docker容器状态："
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.CPUPerc}}\t{{.MemUsage}}"

# 服务端口检查
echo ""
echo "🔌 服务端口状态："
ports=(80 3000 8080 8081 9000 9001 19071)
for port in "${ports[@]}"; do
    if netstat -tuln | grep -q ":$port "; then
        echo "端口 $port: ✅ 开放"
    else
        echo "端口 $port: ❌ 关闭"
    fi
done

# 数据库连接检查
echo ""
echo "🗄️ 数据库连接状态："
if PGPASSWORD="Sygy@2025" psql -h ********** -U km_user -d "knowledge-manage" -c "SELECT 1;" &>/dev/null; then
    echo "PostgreSQL: ✅ 连接正常"
else
    echo "PostgreSQL: ❌ 连接异常"
fi

if redis-cli -h ********** -p 6379 ping &>/dev/null; then
    echo "Redis: ✅ 连接正常"
else
    echo "Redis: ❌ 连接异常"
fi

echo ""
echo "📈 监控完成 - $(date)"
EOF

chmod +x monitor_system.sh
```

## 🐛 故障排除指南

### 常见问题及解决方案

#### 1. PostgreSQL连接失败
```bash
# 检查PostgreSQL服务状态
sudo systemctl status postgresql-15

# 检查网络连通性
ping **********

# 检查端口开放
nc -z ********** 5432

# 检查防火墙设置
sudo firewall-cmd --list-ports
sudo firewall-cmd --list-rich-rules

# 查看PostgreSQL日志
sudo tail -f /var/lib/pgsql/15/data/log/postgresql-*.log

# 重启PostgreSQL服务
sudo systemctl restart postgresql-15
```

#### 2. Docker容器启动失败
```bash
# 查看容器状态
docker-compose -f docker-compose.main.yml ps

# 查看详细日志
docker-compose -f docker-compose.main.yml logs [service-name]

# 检查镜像是否存在
docker images

# 重新构建镜像
docker-compose -f docker-compose.main.yml build --no-cache [service-name]

# 检查资源使用
free -h
df -h
```

#### 3. Vespa搜索引擎启动慢
```bash
# Vespa需要较长启动时间，请耐心等待
# 查看Vespa启动日志
docker logs km-vespa -f

# 检查Vespa状态
curl http://localhost:8081/ApplicationStatus

# 如果启动失败，增加内存分配
# 编辑docker-compose.main.yml，在vespa服务中添加：
# deploy:
#   resources:
#     limits:
#       memory: 4G
#     reservations:
#       memory: 2G
```

#### 4. AI模型服务器(model_server)启动失败
```bash
# 检查model_server容器状态
docker-compose -f docker-compose.main.yml ps model_server

# 查看model_server详细日志
docker-compose -f docker-compose.main.yml logs model_server -f

# 检查model_server健康状态
curl -f http://localhost:9000/health

# 检查Redis连接
redis-cli -h ********** -p 6379 ping

# 检查模型缓存目录权限
ls -la /opt/km/model_cache
sudo chown -R 1000:1000 /opt/km/model_cache

# 重启model_server服务
docker-compose -f docker-compose.main.yml restart model_server

# 如果内存不足，可以调整资源限制
# 编辑docker-compose.main.yml中model_server的deploy配置
```

#### 5. API服务无法连接model_server
```bash
# 检查model_server是否正常运行
curl -f http://localhost:9000/health

# 检查API服务日志中的model_server连接错误
docker-compose -f docker-compose.main.yml logs api_server | grep model

# 验证环境变量配置
docker exec km-api-server env | grep MODEL_SERVER

# 检查Docker网络连通性
docker exec km-api-server ping model_server

# 重启API服务
docker-compose -f docker-compose.main.yml restart api_server
```

#### 6. 远程AI模型服务启动失败
```bash
# 检查模型缓存目录权限
ls -la /opt/km/model_cache

# 查看模型服务日志
docker logs km-inference-model -f
docker logs km-indexing-model -f

# 检查网络连接（模型下载需要网络）
curl -I https://huggingface.co

# 手动下载模型（如果网络问题）
# 在容器内执行模型下载命令
```

#### 5. 前端服务无法访问
```bash
# 检查前端服务状态
curl http://localhost:3000

# 查看前端服务日志
docker logs km-web-server -f

# 检查Nginx配置
docker exec km-nginx nginx -t

# 重启Nginx服务
docker restart km-nginx
```

### 紧急恢复流程
```bash
# 创建紧急恢复脚本
cat > emergency_recovery.sh << 'EOF'
#!/bin/bash

echo "🚨 开始紧急恢复流程..."

# 停止所有服务
echo "1. 停止所有服务..."
docker-compose -f docker-compose.main.yml down
docker-compose -f ~/km-deploy/docker-compose.db.yml down

# 清理Docker资源
echo "2. 清理Docker资源..."
docker system prune -f

# 重启Docker服务
echo "3. 重启Docker服务..."
sudo systemctl restart docker

# 重启PostgreSQL服务
echo "4. 重启PostgreSQL服务..."
sudo systemctl restart postgresql-15

# 等待服务稳定
sleep 30

# 重新启动数据库服务器服务
echo "5. 启动数据库服务器服务..."
cd ~/km-deploy
docker-compose -f docker-compose.db.yml up -d

# 等待数据库服务稳定
sleep 30

# 重新启动主服务器服务
echo "6. 启动主服务器服务..."
cd ~/km
docker-compose -f docker-compose.main.yml up -d

echo "🔄 紧急恢复完成，请等待服务启动..."
EOF

chmod +x emergency_recovery.sh
```

## 🚀 一键部署脚本

### 数据库服务器一键部署脚本
```bash
# 创建数据库服务器一键部署脚本
cat > deploy_database_server.sh << 'EOF'
#!/bin/bash

set -e

echo "🗄️ 开始部署数据库服务器 (**********)..."

# 检查是否为root用户
if [[ $EUID -eq 0 ]]; then
   echo "❌ 请不要使用root用户运行此脚本"
   exit 1
fi

# 更新系统
echo "📦 更新系统包..."
sudo dnf update -y

# 安装基础工具
echo "🔧 安装基础工具..."
sudo dnf install -y curl wget git vim net-tools firewalld dnf-utils device-mapper-persistent-data lvm2

# 安装Docker
echo "🐳 安装Docker..."
if ! command -v docker &> /dev/null; then
    sudo dnf config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
    sudo dnf install -y docker-ce docker-ce-cli containerd.io
    sudo systemctl start docker
    sudo systemctl enable docker
    sudo usermod -aG docker $USER
fi

# 安装Docker Compose
echo "📦 安装Docker Compose..."
if ! command -v docker-compose &> /dev/null; then
    COMPOSE_VERSION="2.24.0"
    sudo curl -L "https://github.com/docker/compose/releases/download/v${COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    sudo ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
fi

# 安装PostgreSQL
echo "🗄️ 安装PostgreSQL 15..."
if ! command -v psql &> /dev/null; then
    sudo dnf install -y postgresql15-server postgresql15-contrib
    sudo /usr/pgsql-15/bin/postgresql-15-setup initdb
    sudo systemctl enable postgresql-15
    sudo systemctl start postgresql-15
fi

# 配置PostgreSQL
echo "⚙️ 配置PostgreSQL..."
sudo cp /var/lib/pgsql/15/data/postgresql.conf /var/lib/pgsql/15/data/postgresql.conf.backup
sudo cp /var/lib/pgsql/15/data/pg_hba.conf /var/lib/pgsql/15/data/pg_hba.conf.backup

# 修改postgresql.conf
sudo sed -i "s/#listen_addresses = 'localhost'/listen_addresses = '*'/" /var/lib/pgsql/15/data/postgresql.conf
sudo sed -i "s/#port = 5432/port = 5432/" /var/lib/pgsql/15/data/postgresql.conf
sudo sed -i "s/#max_connections = 100/max_connections = 200/" /var/lib/pgsql/15/data/postgresql.conf

# 修改pg_hba.conf
echo "host    all             km_user         **********/32           md5" | sudo tee -a /var/lib/pgsql/15/data/pg_hba.conf
echo "host    all             km_user         *********/24            md5" | sudo tee -a /var/lib/pgsql/15/data/pg_hba.conf
echo "host    all             postgres        *********/24            md5" | sudo tee -a /var/lib/pgsql/15/data/pg_hba.conf

# 重启PostgreSQL
sudo systemctl restart postgresql-15

# 创建数据库和用户
echo "👤 创建数据库和用户..."
sudo -u postgres psql << EOSQL
CREATE DATABASE "knowledge-manage";
CREATE USER km_user WITH PASSWORD 'Sygy@2025';
ALTER USER km_user WITH SUPERUSER CREATEDB CREATEROLE REPLICATION;
GRANT ALL PRIVILEGES ON DATABASE "knowledge-manage" TO km_user;
\c "knowledge-manage";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
GRANT ALL PRIVILEGES ON SCHEMA public TO km_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO km_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO km_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO km_user;
EOSQL

# 配置防火墙
echo "🔥 配置防火墙..."
sudo systemctl start firewalld
sudo systemctl enable firewalld
sudo firewall-cmd --permanent --add-port=5432/tcp
sudo firewall-cmd --permanent --add-port=6379/tcp
sudo firewall-cmd --permanent --add-port=9001/tcp
sudo firewall-cmd --permanent --add-port=9002/tcp
sudo firewall-cmd --reload

# 创建数据目录
echo "📁 创建数据目录..."
sudo mkdir -p /opt/km/data/{redis,minio}
sudo chown -R $USER:$USER /opt/km/data

# 创建Docker Compose配置
echo "📝 创建Docker Compose配置..."
mkdir -p ~/km-deploy
cd ~/km-deploy

cat > docker-compose.db.yml << 'EOCOMPOSE'
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: km-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --bind 0.0.0.0
    volumes:
      - /opt/km/data/redis:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - km_network

  minio:
    image: minio/minio:RELEASE.2024-01-16T16-07-38Z
    container_name: km-minio
    restart: unless-stopped
    ports:
      - "9001:9000"
      - "9002:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - /opt/km/data/minio:/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - km_network

networks:
  km_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
EOCOMPOSE

# 启动服务
echo "🚀 启动Redis和MinIO服务..."
docker-compose -f docker-compose.db.yml up -d

# 验证部署
echo "✅ 验证部署..."
sleep 30

# 测试PostgreSQL
PGPASSWORD="Sygy@2025" psql -h localhost -U km_user -d "knowledge-manage" -c "SELECT 1;" && echo "✅ PostgreSQL正常" || echo "❌ PostgreSQL异常"

# 测试Redis
docker exec km-redis redis-cli ping && echo "✅ Redis正常" || echo "❌ Redis异常"

# 测试MinIO
curl -f http://localhost:9001 && echo "✅ MinIO正常" || echo "❌ MinIO异常"

echo "🎉 数据库服务器部署完成！"
echo "📋 部署信息："
echo "   PostgreSQL: localhost:5432"
echo "   数据库名: knowledge-manage"
echo "   用户名: km_user"
echo "   密码: Sygy@2025"
echo "   Redis: localhost:6379"
echo "   MinIO API: localhost:9001"
echo "   MinIO控制台: localhost:9002 (minioadmin/minioadmin)"
echo ""
echo "⚠️  请重新登录以使Docker用户组生效"
EOF

chmod +x deploy_database_server.sh
```

### 主服务器一键部署脚本
```bash
# 创建主服务器一键部署脚本
cat > deploy_main_server.sh << 'EOF'
#!/bin/bash

set -e

echo "🚀 开始部署主服务器 (**********)..."

# 检查是否为root用户
if [[ $EUID -eq 0 ]]; then
   echo "❌ 请不要使用root用户运行此脚本"
   exit 1
fi

# 检查数据库服务器连接
echo "🔍 检查数据库服务器连接..."
if ! ping -c 3 ********** &>/dev/null; then
    echo "❌ 无法连接到数据库服务器 **********"
    exit 1
fi

if ! nc -z ********** 5432; then
    echo "❌ 数据库服务器PostgreSQL端口5432未开放"
    exit 1
fi

# 更新系统
echo "📦 更新系统包..."
sudo dnf update -y

# 安装基础工具
echo "🔧 安装基础工具..."
sudo dnf install -y curl wget git vim net-tools firewalld dnf-utils device-mapper-persistent-data lvm2 postgresql15

# 安装Docker
echo "🐳 安装Docker..."
if ! command -v docker &> /dev/null; then
    sudo dnf config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
    sudo dnf install -y docker-ce docker-ce-cli containerd.io
    sudo systemctl start docker
    sudo systemctl enable docker
    sudo usermod -aG docker $USER
fi

# 安装Docker Compose
echo "📦 安装Docker Compose..."
if ! command -v docker-compose &> /dev/null; then
    COMPOSE_VERSION="2.24.0"
    sudo curl -L "https://github.com/docker/compose/releases/download/v${COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    sudo ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
fi

# 获取项目代码
echo "📥 获取项目代码..."
if [ ! -d "knowledgemanager" ]; then
    echo "请先克隆项目代码到当前目录并切换到dev分支"
    echo "git clone <repository-url> knowledgemanager"
    echo "cd knowledgemanager && git checkout dev"
    exit 1
fi

cd knowledgemanager

# 检查当前分支
current_branch=$(git branch --show-current)
if [ "$current_branch" != "dev" ]; then
    echo "⚠️ 当前分支: $current_branch"
    echo "建议使用dev分支（最新代码）"
    echo "执行: git checkout dev && git pull origin dev"
    read -p "是否继续使用当前分支? (y/N): " confirm
    if [[ $confirm != [yY] ]]; then
        exit 1
    fi
else
    echo "✅ 当前使用dev分支"
fi

# 创建环境配置
echo "⚙️ 创建环境配置..."
cat > .env.main << 'EOENV'
POSTGRES_HOST=**********
POSTGRES_PORT=5432
POSTGRES_USER=km_user
POSTGRES_PASSWORD=Sygy@2025
POSTGRES_DB=knowledge-manage

REDIS_HOST=**********
REDIS_PORT=6379
REDIS_PASSWORD=

VESPA_HOST=**********
VESPA_PORT=8081
VESPA_TENANT_PORT=19071

S3_ENDPOINT_URL=http://**********:9001
S3_AWS_ACCESS_KEY_ID=minioadmin
S3_AWS_SECRET_ACCESS_KEY=minioadmin

# ========== 内网大模型配置 ==========

# 推理模型配置（对话生成）- qwen3:32b
GEN_AI_PROVIDER=openai
GEN_AI_API_BASE=http://***********:9997/qwen3/v1
GEN_AI_API_KEY=dummy-key
GEN_AI_MODEL_NAME=qwen3:32b
GEN_AI_MAX_TOKENS=32768

# 重排模型配置（搜索优化）- qwen3-reranker-8b
RERANK_PROVIDER=openai
RERANK_API_BASE=http://***********:8000/v1
RERANK_API_KEY=dummy-key
RERANK_MODEL_NAME=qwen3-reranker-8b

# 嵌入模型配置（向量化）- bge-large-zh-v1.5
EMBEDDING_PROVIDER=custom
EMBEDDING_API_BASE=http://***********:9997/v1
EMBEDDING_API_KEY=dummy-key
EMBEDDING_MODEL_NAME=bge-large-zh-v1.5
EMBEDDING_MAX_CONTEXT=3072

# 兼容性配置
EMBEDDING_MODEL_SERVER_HOST=***********
EMBEDDING_MODEL_SERVER_PORT=9997
INFERENCE_MODEL_SERVER_HOST=***********
INFERENCE_MODEL_SERVER_PORT=9997

WEB_DOMAIN=http://**********:3000
CORS_ALLOWED_ORIGIN=http://**********
NEXT_PUBLIC_API_URL=http://**********:8080
NEXT_PUBLIC_DISABLE_STREAMING=false

AUTH_TYPE=disabled
SECRET=your-secret-key-here-change-in-production
ENCRYPTION_KEY_SECRET=your-encryption-key-here-change-in-production

LOG_LEVEL=info
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1

MODEL_CACHE_DIR=/app/model_cache
HF_HOME=/app/model_cache
TRANSFORMERS_CACHE=/app/model_cache

CELERY_BROKER_URL=redis://**********:6379/0
CELERY_RESULT_BACKEND=redis://**********:6379/0
EOENV

# 创建数据目录
echo "📁 创建数据目录..."
sudo mkdir -p /opt/km/{vespa,model_cache,logs,nginx/conf.d}
sudo chown -R $USER:$USER /opt/km

# 配置防火墙
echo "🔥 配置防火墙..."
sudo systemctl start firewalld
sudo systemctl enable firewalld
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --permanent --add-port=8081/tcp
sudo firewall-cmd --permanent --add-port=9000/tcp
sudo firewall-cmd --permanent --add-port=9001/tcp
sudo firewall-cmd --permanent --add-port=19071/tcp
sudo firewall-cmd --reload

# 测试数据库连接
echo "🔍 测试数据库连接..."
PGPASSWORD="Sygy@2025" psql -h ********** -U km_user -d "knowledge-manage" -c "SELECT 1;" && echo "✅ 数据库连接正常" || (echo "❌ 数据库连接失败" && exit 1)

# 构建Docker镜像
echo "🏗️ 构建Docker镜像..."
docker-compose -f docker-compose.main.yml build --no-cache

# 启动服务
echo "🚀 启动服务..."
./deploy_main_server_services.sh

echo "🎉 主服务器部署完成！"
echo "📋 访问地址："
echo "   主页面: http://**********"
echo "   API文档: http://**********/api/docs"
echo "   直接前端: http://**********:3000"
echo "   直接API: http://**********:8080"
EOF

chmod +x deploy_main_server.sh
```

## 📚 部署总结

### 🎯 部署检查清单

#### 数据库服务器 (**********) 检查清单
- [ ] 系统更新完成
- [ ] Docker和Docker Compose安装完成
- [ ] PostgreSQL 15安装并配置完成
- [ ] 数据库"knowledge-manage"创建完成
- [ ] 用户km_user创建并授权完成
- [ ] 防火墙端口开放完成
- [ ] Redis容器启动正常
- [ ] MinIO容器启动正常
- [ ] 远程连接测试通过

#### 主服务器 (**********) 检查清单
- [ ] 系统更新完成
- [ ] Docker和Docker Compose安装完成
- [ ] 项目代码获取完成
- [ ] 环境变量配置完成
- [ ] 数据目录创建完成
- [ ] 防火墙端口开放完成
- [ ] Docker镜像构建完成
- [ ] Vespa搜索引擎启动正常
- [ ] API后端服务启动正常
- [ ] Web前端服务启动正常
- [ ] Nginx代理启动正常
- [ ] 数据库迁移执行完成
- [ ] 系统验证测试通过

#### 内网大模型服务检查清单
- [ ] 推理模型服务(qwen3:32b)连接正常
- [ ] 重排模型服务(qwen3-reranker-8b)连接正常
- [ ] 嵌入模型服务(bge-large-zh-v1.5)连接正常
- [ ] 大模型配置已通过Web UI或环境变量完成
- [ ] 模型API兼容性验证通过
- [ ] 模型响应时间测试通过
- [ ] 集成测试验证通过

### 🔗 重要访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| 主页面 | http://********** | 通过Nginx代理访问 |
| API文档 | http://**********/api/docs | Swagger API文档 |
| 直接前端 | http://**********:3000 | 直接访问前端服务 |
| 直接API | http://**********:8080 | 直接访问API服务 |
| MinIO控制台 | http://**********:9002 | 文件存储管理界面 |

### 📞 技术支持

如果在部署过程中遇到问题，请：

1. **查看日志**: 使用`docker-compose logs`命令查看详细错误信息
2. **检查网络**: 确保两台服务器之间网络连通正常
3. **验证配置**: 检查环境变量和配置文件是否正确
4. **资源检查**: 确保服务器有足够的CPU、内存和磁盘空间
5. **运行验证脚本**: 使用提供的验证脚本检查系统状态

---

**文档版本**: v2.1
**最后更新**: 2025-08-25
**适用版本**: KM v1.0+
**服务器配置**: openEuler 20.03 LTS 双服务器
**数据库配置**: PostgreSQL 15, 数据库名: knowledge-manage, 用户: km_user
**大模型配置**:
- 推理模型: qwen3:32b (***********:9997)
- 重排模型: qwen3-reranker-8b (***********:8000)
- 嵌入模型: bge-large-zh-v1.5 (***********:9997)
