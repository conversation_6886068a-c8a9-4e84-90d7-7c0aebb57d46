# Onyx双服务器集群部署配置详解

## 🏗️ 架构概述

### 服务器资源分配

| 服务器 | IP地址 | 配置 | 主要服务 |
|--------|--------|------|----------|
| 主服务器 | ********** | CPU:8核, 内存:32G, 磁盘:100G | 应用服务、搜索引擎、对象存储 |
| 数据库服务器 | ********** | CPU:8核, 内存:32G, 磁盘:100G | PostgreSQL数据库 |

### 服务分布详情

#### 主服务器 (**********) 服务清单

| 服务名称 | 容器名 | 端口 | 说明 |
|----------|--------|------|------|
| Nginx反向代理 | km-nginx | 80, 443 | 前端入口，负载均衡 |
| Web前端 | km-web-server | 3000 | Next.js前端应用 |
| API后端 | km-api-server | 8080 | FastAPI后端服务 |
| 推理模型服务器 | km-inference-model | 9000 | AI模型推理服务 |
| 索引模型服务器 | km-indexing-model | 9001 | 文档索引模型服务 |
| Vespa搜索引擎 | km-vespa | 8081, 19071 | 混合检索搜索引擎 |
| 后台任务处理 | km-background | - | Celery后台任务 |

#### 数据库服务器 (**********) 服务清单

| 服务名称 | 安装方式 | 端口 | 说明 |
|----------|----------|------|------|
| PostgreSQL数据库（版本：15） | 宿主机直接安装 | 5432 | 主数据库，支持远程访问 |
| MinIO对象存储（版本：latest） | km-minio | 9001, 9002 | 文件存储服务 |
| Redis缓存（版本：6.2） | km-redis | 6379 | 缓存服务，无密码 |

## ⚙️ 配置文件详解

### 主服务器环境配置 (.env.main)

```bash
# 数据库配置（连接到远程数据库）
POSTGRES_HOST=**********
POSTGRES_PORT=5432
POSTGRES_USER=onyx_user
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_DB=onyx

# Redis配置（连接到数据库服务器）
REDIS_HOST=**********
REDIS_PORT=6379
REDIS_PASSWORD=  # 无密码

# Vespa搜索引擎配置
VESPA_HOST=**********
VESPA_PORT=8081
VESPA_TENANT_PORT=19071

# MinIO对象存储配置（连接到数据库服务器）
S3_ENDPOINT_URL=http://**********:9001
S3_AWS_ACCESS_KEY_ID=minioadmin
S3_AWS_SECRET_ACCESS_KEY=minioadmin

# AI模型配置
GEN_AI_API_KEY=your-openai-api-key-here
EMBEDDING_MODEL_SERVER_HOST=**********
EMBEDDING_MODEL_SERVER_PORT=9001

# Web服务配置
WEB_DOMAIN=http://**********:3000
CORS_ALLOWED_ORIGIN=http://**********

# 认证配置
AUTH_TYPE=disabled
SECRET=your-secret-key-here
ENCRYPTION_KEY_SECRET=your-encryption-key-here

# 日志配置
LOG_LEVEL=info
```

### 数据库服务器环境配置 (.env.db)

```bash
# PostgreSQL配置
POSTGRES_HOST=0.0.0.0  # 监听所有接口
POSTGRES_PORT=5432
POSTGRES_USER=onyx_user
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_DB=onyx

# Redis配置
REDIS_HOST=0.0.0.0  # 监听所有接口
REDIS_PORT=6379
REDIS_PASSWORD=  # 无密码

# MinIO对象存储配置
S3_ENDPOINT_URL=http://0.0.0.0:9001
S3_AWS_ACCESS_KEY_ID=minioadmin
S3_AWS_SECRET_ACCESS_KEY=minioadmin

# 认证配置
POSTGRES_HOST_AUTH_METHOD=md5
POSTGRES_INITDB_ARGS=--auth-host=md5

# 日志配置
LOG_LEVEL=info
```

## 🔧 Docker Compose配置

### 主服务器配置 (docker-compose.main.yml)

关键配置要点：

1. **网络配置**: 所有服务使用自定义网络 `onyx_network`
2. **依赖关系**: 严格定义服务启动顺序
3. **健康检查**: 每个服务都配置健康检查
4. **资源限制**: 根据服务器配置设置资源限制

```yaml
# 示例：API服务配置
api_server:
  build:
    context: .
    dockerfile: docker/Dockerfile.backend
  container_name: km-api-server
  ports:
    - "8080:8080"
  environment:
    - POSTGRES_HOST=**********  # 连接远程数据库
    - REDIS_HOST=**********     # 连接远程Redis
    - VESPA_HOST=**********     # 使用本地Vespa
  env_file:
    - .env.main
  restart: unless-stopped
  depends_on:
    - inference_model_server
    - indexing_model_server
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
    interval: 30s
    timeout: 10s
    retries: 3
```

### 数据库服务器配置 (docker-compose.db.yml)

**注意：PostgreSQL数据库现在安装在宿主机上，不再使用容器方式**

```yaml
# PostgreSQL数据库已改为宿主机安装，此配置已移除

# Redis缓存配置
cache:
  image: redis:7-alpine
  container_name: km-redis
  ports:
    - "6379:6379"
  command: redis-server --appendonly yes
  volumes:
    - redis_data:/data
  restart: unless-stopped
  healthcheck:
    test: ["CMD", "redis-cli", "ping"]
    interval: 30s
    timeout: 10s
    retries: 3

# MinIO对象存储配置
minio:
  image: minio/minio:RELEASE.2024-01-16T16-07-38Z
  container_name: km-minio
  ports:
    - "9001:9000"
    - "9002:9001"
  environment:
    MINIO_ROOT_USER: minioadmin
    MINIO_ROOT_PASSWORD: minioadmin
  command: server /data --console-address ":9001"
  volumes:
    - minio_data:/data
  restart: unless-stopped
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
    interval: 30s
    timeout: 10s
    retries: 3
```

## 🔒 安全配置

### PostgreSQL宿主机安装配置

#### 1. PostgreSQL 15 宿主机安装

```bash
# 在数据库服务器 (**********) 上执行

# 1. 安装PostgreSQL 15
sudo dnf install -y postgresql15-server postgresql15-contrib

# 2. 初始化数据库
sudo /usr/pgsql-15/bin/postgresql-15-setup initdb

# 3. 启动并设置开机自启
sudo systemctl enable postgresql-15
sudo systemctl start postgresql-15

# 4. 检查服务状态
sudo systemctl status postgresql-15
```

#### 2. PostgreSQL配置文件修改

##### postgresql.conf 关键配置
配置文件位置：`/var/lib/pgsql/15/data/postgresql.conf`

```bash
# 编辑配置文件
sudo vim /var/lib/pgsql/15/data/postgresql.conf

# 修改以下配置项：
```

```conf
# 网络连接配置
listen_addresses = '*'          # 监听所有IP地址
port = 5432                     # 默认端口

# 连接配置
max_connections = 200           # 最大连接数
shared_buffers = 256MB          # 共享缓冲区
effective_cache_size = 1GB      # 有效缓存大小

# 安全配置
ssl = off                       # 生产环境建议开启SSL
password_encryption = md5       # 密码加密方式

# 日志配置
log_statement = 'all'           # 记录所有SQL语句
log_min_duration_statement = 1000  # 记录慢查询
```

##### pg_hba.conf 访问控制配置
配置文件位置：`/var/lib/pgsql/15/data/pg_hba.conf`

```bash
# 编辑访问控制文件
sudo vim /var/lib/pgsql/15/data/pg_hba.conf

# 添加以下配置：
```

```conf
# TYPE  DATABASE        USER            ADDRESS                 METHOD

# 本地连接
local   all             all                                     trust
host    all             all             127.0.0.1/32            md5
host    all             all             ::1/128                 md5

# 允许主服务器连接
host    all             onyx_user       **********/32           md5

# 允许内网连接
host    all             onyx_user       *********/24            md5

# 生产环境可以更严格地限制访问
# host    onyx            onyx_user       **********/32           md5
```

#### 3. 创建数据库和用户

```bash
# 切换到postgres用户
sudo -u postgres psql

# 在PostgreSQL命令行中执行：
```

```sql
-- 创建数据库
CREATE DATABASE onyx;

-- 创建用户
CREATE USER onyx_user WITH PASSWORD 'your_secure_password_here';

-- 授权
GRANT ALL PRIVILEGES ON DATABASE onyx TO onyx_user;

-- 连接到onyx数据库
\c onyx;

-- 创建必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- 授权给onyx_user
GRANT ALL PRIVILEGES ON SCHEMA public TO onyx_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO onyx_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO onyx_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO onyx_user;

-- 退出
\q
```

#### 4. 重启PostgreSQL服务

```bash
# 重启服务使配置生效
sudo systemctl restart postgresql-15

# 验证服务状态
sudo systemctl status postgresql-15

# 测试本地连接
sudo -u postgres psql -c "SELECT version();"

# 测试远程连接（从主服务器测试）
PGPASSWORD="your_secure_password_here" psql -h ********** -U onyx_user -d onyx -c "SELECT 1;"
```

### 防火墙配置

#### 主服务器防火墙规则

```bash
# 开放Web访问端口
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp

# 开放API端口（可选，通过nginx代理访问）
sudo firewall-cmd --permanent --add-port=8080/tcp

# 开放MinIO端口
sudo firewall-cmd --permanent --add-port=9001/tcp
sudo firewall-cmd --permanent --add-port=9002/tcp

# 开放Vespa端口
sudo firewall-cmd --permanent --add-port=8081/tcp

# 重载防火墙配置
sudo firewall-cmd --reload
```

#### 数据库服务器防火墙规则

```bash
# 开放PostgreSQL端口
sudo firewall-cmd --permanent --add-port=5432/tcp

# 开放Redis端口
sudo firewall-cmd --permanent --add-port=6379/tcp

# 开放MinIO端口
sudo firewall-cmd --permanent --add-port=9001/tcp
sudo firewall-cmd --permanent --add-port=9002/tcp

# 限制访问源（可选）
sudo firewall-cmd --permanent --add-rich-rule="rule family='ipv4' source address='**********/32' port protocol='tcp' port='5432' accept"
sudo firewall-cmd --permanent --add-rich-rule="rule family='ipv4' source address='**********/32' port protocol='tcp' port='6379' accept"
sudo firewall-cmd --permanent --add-rich-rule="rule family='ipv4' source address='**********/32' port protocol='tcp' port='9001' accept"

# 重载防火墙配置
sudo firewall-cmd --reload
```

## 🚀 主服务器 (**********) 服务安装与启动详解

### 1. 环境准备阶段

```bash
# 在主服务器 (**********) 上执行
git clone <repository-url> onyx
cd onyx
chmod +x deploy-openeuler-cluster.sh verify-cluster-deployment.sh

# 安装Docker和Docker Compose
sudo dnf install -y docker docker-compose
sudo systemctl enable docker
sudo systemctl start docker
sudo usermod -aG docker $USER

# 重新登录以使用户组生效
```

### 2. 主服务器各服务详细安装启动说明

#### 2.1 Web前端服务 (km-web-server)

**服务说明**: Next.js前端应用，提供用户界面
**端口**: 3000
**容器名**: km-web-server

```bash
# 构建前端镜像
cd web
docker build -t onyx-web-frontend .

# 启动前端服务
docker run -d \
  --name km-web-server \
  --restart unless-stopped \
  -p 3000:3000 \
  --env-file ../.env.main \
  -e NEXT_PUBLIC_API_URL=http://**********:8080 \
  -e NEXT_PUBLIC_DISABLE_STREAMING=false \
  onyx-web-frontend

# 验证服务状态
docker logs km-web-server
curl -f http://localhost:3000
```

#### 2.2 API后端服务 (km-api-server)

**服务说明**: FastAPI后端服务，提供核心API接口
**端口**: 8080
**容器名**: km-api-server

```bash
# 构建后端镜像
cd backend
docker build -t onyx-api-backend .

# 启动API服务
docker run -d \
  --name km-api-server \
  --restart unless-stopped \
  -p 8080:8080 \
  --env-file ../.env.main \
  -e POSTGRES_HOST=********** \
  -e REDIS_HOST=********** \
  -e VESPA_HOST=********** \
  -e MODEL_SERVER_HOST=********** \
  -e INDEXING_MODEL_SERVER_HOST=********** \
  onyx-api-backend

# 执行数据库迁移
docker exec km-api-server alembic upgrade head

# 验证服务状态
docker logs km-api-server
curl -f http://localhost:8080/health
```

#### 2.3 推理模型服务器 (km-inference-model)

**服务说明**: AI模型推理服务，处理文本生成和问答
**端口**: 9000
**容器名**: km-inference-model

```bash
# 构建推理模型镜像
cd backend
docker build -f Dockerfile.model_server -t onyx-inference-model .

# 启动推理模型服务
docker run -d \
  --name km-inference-model \
  --restart unless-stopped \
  -p 9000:9000 \
  --env-file ../.env.main \
  -e MODEL_SERVER_PORT=9000 \
  -e EMBEDDING_MODEL_SERVER_HOST=0.0.0.0 \
  -e EMBEDDING_MODEL_SERVER_PORT=9000 \
  onyx-inference-model

# 验证服务状态
docker logs km-inference-model
curl -f http://localhost:9000/health
```

#### 2.4 索引模型服务器 (km-indexing-model)

**服务说明**: 文档索引模型服务，处理文档嵌入和索引
**端口**: 9001
**容器名**: km-indexing-model

```bash
# 构建索引模型镜像
cd backend
docker build -f Dockerfile.model_server -t onyx-indexing-model .

# 启动索引模型服务
docker run -d \
  --name km-indexing-model \
  --restart unless-stopped \
  -p 9001:9001 \
  --env-file ../.env.main \
  -e MODEL_SERVER_PORT=9001 \
  -e EMBEDDING_MODEL_SERVER_HOST=0.0.0.0 \
  -e EMBEDDING_MODEL_SERVER_PORT=9001 \
  onyx-indexing-model

# 验证服务状态
docker logs km-indexing-model
curl -f http://localhost:9001/health
```

#### 2.5 Vespa搜索引擎 (km-vespa)

**服务说明**: 混合检索搜索引擎，提供文档搜索和检索功能
**端口**: 8081, 19071
**容器名**: km-vespa

```bash
# 拉取Vespa镜像
docker pull vespaengine/vespa:8.526.15

# 创建Vespa数据目录
sudo mkdir -p /opt/vespa/data
sudo chown -R 1000:1000 /opt/vespa/data

# 启动Vespa服务
docker run -d \
  --name km-vespa \
  --restart unless-stopped \
  -p 8081:8080 \
  -p 19071:19071 \
  -v /opt/vespa/data:/opt/vespa/var \
  --env-file ../.env.main \
  vespaengine/vespa:8.526.15

# 等待Vespa启动完成
sleep 60

# 验证服务状态
docker logs km-vespa
curl -f http://localhost:8081/ApplicationStatus
curl -f http://localhost:19071/
```

#### 2.6 后台任务处理 (km-background)

**服务说明**: Celery后台任务处理器，处理异步任务
**容器名**: km-background

```bash
# 启动后台任务处理器
docker run -d \
  --name km-background \
  --restart unless-stopped \
  --env-file ../.env.main \
  -e POSTGRES_HOST=********** \
  -e REDIS_HOST=********** \
  -e VESPA_HOST=********** \
  onyx-api-backend \
  /bin/sh -c "supervisord -c /etc/supervisor/conf.d/supervisord.conf"

# 验证服务状态
docker logs km-background
```

#### 2.7 Nginx反向代理 (km-nginx)

**服务说明**: 前端入口，负载均衡和反向代理
**端口**: 80, 443
**容器名**: km-nginx

```bash
# 创建Nginx配置目录
sudo mkdir -p /etc/nginx/conf.d

# 创建Nginx配置文件
sudo tee /etc/nginx/conf.d/onyx.conf > /dev/null << 'EOF'
server {
    listen 80;
    server_name _;

    location / {
        proxy_pass http://**********:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/ {
        proxy_pass http://**********:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# 启动Nginx服务
docker run -d \
  --name km-nginx \
  --restart unless-stopped \
  -p 80:80 \
  -p 443:443 \
  -v /etc/nginx/conf.d:/etc/nginx/conf.d:ro \
  nginx:alpine

# 验证服务状态
docker logs km-nginx
curl -f http://localhost
```

### 3. 服务启动顺序和依赖关系

**重要**: 必须按照以下顺序启动服务，确保依赖关系正确：

```bash
# 第一步：启动基础服务（在数据库服务器上）
# 1. PostgreSQL (宿主机安装，已在前面完成)
# 2. Redis缓存
# 3. MinIO对象存储

# 第二步：启动搜索引擎（在主服务器上）
# 4. Vespa搜索引擎

# 第三步：启动AI模型服务（在主服务器上）
# 5. 推理模型服务器 (km-inference-model)
# 6. 索引模型服务器 (km-indexing-model)

# 第四步：启动应用服务（在主服务器上）
# 7. API后端服务 (km-api-server)
# 8. 后台任务处理 (km-background)
# 9. Web前端服务 (km-web-server)

# 第五步：启动代理服务（在主服务器上）
# 10. Nginx反向代理 (km-nginx)
```

### 4. 一键启动脚本

创建主服务器启动脚本 `start-main-services.sh`：

```bash
#!/bin/bash

# 主服务器服务启动脚本
set -e

echo "🚀 启动主服务器所有服务..."

# 1. 启动Vespa搜索引擎
echo "📊 启动Vespa搜索引擎..."
docker start km-vespa || docker run -d --name km-vespa --restart unless-stopped -p 8081:8080 -p 19071:19071 -v /opt/vespa/data:/opt/vespa/var vespaengine/vespa:8.526.15
sleep 30

# 2. 启动推理模型服务器
echo "🧠 启动推理模型服务器..."
docker start km-inference-model || echo "请先构建并创建推理模型容器"
sleep 20

# 3. 启动索引模型服务器
echo "📚 启动索引模型服务器..."
docker start km-indexing-model || echo "请先构建并创建索引模型容器"
sleep 20

# 4. 启动API后端服务
echo "⚙️ 启动API后端服务..."
docker start km-api-server || echo "请先构建并创建API服务容器"
sleep 15

# 5. 启动后台任务处理
echo "🔄 启动后台任务处理..."
docker start km-background || echo "请先构建并创建后台任务容器"
sleep 10

# 6. 启动Web前端服务
echo "🌐 启动Web前端服务..."
docker start km-web-server || echo "请先构建并创建前端服务容器"
sleep 15

# 7. 启动Nginx代理
echo "🔀 启动Nginx反向代理..."
docker start km-nginx || echo "请先创建Nginx容器"

echo "✅ 所有服务启动完成！"
echo "🌐 访问地址: http://**********"
```

### 5. 配置阶段

#### 主服务器配置 (.env.main)
```bash
# 编辑主服务器环境配置
vim .env.main

# 确保以下配置正确：
POSTGRES_HOST=**********
REDIS_HOST=**********
VESPA_HOST=**********
MODEL_SERVER_HOST=**********
INDEXING_MODEL_SERVER_HOST=**********
S3_ENDPOINT_URL=http://**********:9001

# 关键配置项检查
grep -E "POSTGRES_HOST|REDIS_HOST|VESPA_HOST" .env.main
```

#### 数据库服务器配置 (.env.db)
```bash
# 编辑数据库服务器环境配置（仅用于Redis和MinIO）
vim .env.db

# 确保密码与主服务器一致
grep POSTGRES_PASSWORD .env.db
```

### 6. 部署阶段

#### 先部署数据库服务器 (**********)
```bash
# 在数据库服务器上执行

# 1. PostgreSQL已通过宿主机安装完成

# 2. 启动Redis缓存
docker run -d \
  --name km-redis \
  --restart unless-stopped \
  -p 6379:6379 \
  redis:7-alpine redis-server --appendonly yes

# 3. 启动MinIO对象存储
docker run -d \
  --name km-minio \
  --restart unless-stopped \
  -p 9001:9000 \
  -p 9002:9001 \
  -e MINIO_ROOT_USER=minioadmin \
  -e MINIO_ROOT_PASSWORD=minioadmin \
  -v /opt/minio/data:/data \
  minio/minio:RELEASE.2024-01-16T16-07-38Z \
  server /data --console-address ":9001"

# 验证服务状态
docker ps
sudo systemctl status postgresql-15
```

#### 再部署主服务器 (**********)
```bash
# 在主服务器上执行

# 1. 按照前面的详细说明构建和启动各个服务
# 2. 或者使用一键启动脚本
chmod +x start-main-services.sh
./start-main-services.sh

# 验证服务启动
docker ps
curl -f http://localhost:8080/health
curl -f http://localhost:3000
curl -f http://localhost
```

### 7. 验证阶段

```bash
# 在主服务器上执行完整验证
echo "🔍 验证所有服务状态..."

# 检查数据库连接
PGPASSWORD="your_secure_password_here" psql -h ********** -U onyx_user -d onyx -c "SELECT 1;"

# 检查Redis连接
redis-cli -h ********** -p 6379 ping

# 检查MinIO连接
curl -f http://**********:9001

# 检查Vespa搜索引擎
curl -f http://localhost:8081/ApplicationStatus

# 检查API服务
curl -f http://localhost:8080/health

# 检查前端服务
curl -f http://localhost:3000

# 检查Nginx代理
curl -f http://localhost

echo "✅ 所有服务验证完成！"
```

## 🔍 监控与维护

### 服务监控命令

#### 主服务器 (**********) 监控

```bash
# 查看所有容器状态
docker ps -a

# 查看特定服务日志
docker logs km-api-server -f
docker logs km-web-server -f
docker logs km-inference-model -f
docker logs km-indexing-model -f
docker logs km-vespa -f
docker logs km-background -f
docker logs km-nginx -f

# 查看资源使用情况
docker stats

# 检查服务健康状态
curl -f http://localhost:8080/health  # API服务
curl -f http://localhost:3000         # 前端服务
curl -f http://localhost:8081/ApplicationStatus  # Vespa搜索
curl -f http://localhost:9000/health  # 推理模型
curl -f http://localhost:9001/health  # 索引模型
curl -f http://localhost              # Nginx代理

# 查看系统资源
free -h
df -h
top
```

#### 数据库服务器 (**********) 监控

```bash
# 查看PostgreSQL状态
sudo systemctl status postgresql-15

# 查看PostgreSQL进程
ps aux | grep postgres

# 查看数据库连接数
sudo -u postgres psql -d onyx -c "SELECT count(*) FROM pg_stat_activity;"

# 查看数据库大小
sudo -u postgres psql -d onyx -c "SELECT pg_size_pretty(pg_database_size('onyx'));"

# 查看容器状态（Redis和MinIO）
docker ps -a

# 查看Redis状态
docker exec km-redis redis-cli info server
docker exec km-redis redis-cli info memory

# 查看MinIO状态
docker logs km-minio

# 查看系统资源
free -h
df -h
iostat -x 1
```

### 数据库维护

#### PostgreSQL宿主机维护

```bash
# 连接数据库（在数据库服务器**********上）
sudo -u postgres psql -d onyx

# 或者使用onyx_user连接
PGPASSWORD="your_secure_password_here" psql -h localhost -U onyx_user -d onyx

# 备份数据库
sudo -u postgres pg_dump onyx > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复数据库
sudo -u postgres psql onyx < backup.sql

# 查看数据库大小
sudo -u postgres psql -d onyx -c "SELECT pg_size_pretty(pg_database_size('onyx'));"

# 查看数据库连接数
sudo -u postgres psql -d onyx -c "SELECT count(*) FROM pg_stat_activity;"

# 查看数据库状态
sudo systemctl status postgresql-15

# 重启PostgreSQL服务
sudo systemctl restart postgresql-15

# 查看PostgreSQL日志
sudo tail -f /var/lib/pgsql/15/data/log/postgresql-*.log
```

#### Redis和MinIO维护

```bash
# 连接Redis（在数据库服务器**********上）
docker exec -it km-redis redis-cli

# 查看Redis信息
docker exec km-redis redis-cli info

# 从主服务器连接Redis
redis-cli -h ********** -p 6379

# 连接MinIO（通过浏览器访问控制台）
# http://**********:9002
# 用户名: minioadmin
# 密码: minioadmin
```

### 性能优化建议

#### 主服务器优化
1. **内存分配**: 为Vespa和模型服务器分配足够内存
2. **磁盘IO**: 使用SSD存储提升性能
3. **网络优化**: 调整网络缓冲区大小

#### 数据库服务器优化
1. **PostgreSQL调优**: 根据32GB内存调整配置参数
2. **连接池**: 配置合适的连接池大小
3. **索引优化**: 定期分析和优化索引

## 🐛 故障排除

### 常见问题及解决方案

#### 1. PostgreSQL数据库连接失败
```bash
# 检查网络连通性
ping **********

# 检查端口开放
nc -z ********** 5432

# 检查PostgreSQL服务状态（在数据库服务器上）
sudo systemctl status postgresql-15

# 检查PostgreSQL进程
ps aux | grep postgres

# 检查PostgreSQL日志（在数据库服务器上）
sudo tail -f /var/lib/pgsql/15/data/log/postgresql-*.log

# 检查配置文件
sudo cat /var/lib/pgsql/15/data/postgresql.conf | grep listen_addresses
sudo cat /var/lib/pgsql/15/data/pg_hba.conf | grep onyx_user

# 测试本地连接（在数据库服务器上）
sudo -u postgres psql -c "SELECT 1;"

# 测试远程连接（从主服务器）
PGPASSWORD="your_secure_password_here" psql -h ********** -U onyx_user -d onyx -c "SELECT 1;"

# 重启PostgreSQL服务（如果需要）
sudo systemctl restart postgresql-15

# 检查防火墙设置
sudo firewall-cmd --list-ports
sudo firewall-cmd --list-rich-rules
```

#### 2. Redis连接问题
```bash
# 检查Redis容器状态
docker ps | grep km-redis

# 测试Redis连接
docker exec km-redis redis-cli ping

# 检查Redis配置
docker exec km-redis redis-cli CONFIG GET "*"

# 从主服务器测试Redis连接
redis-cli -h ********** -p 6379 ping
```

#### 3. 服务启动失败
```bash
# 查看详细错误信息
docker-compose -f deployment/docker_compose/docker-compose.main.yml logs --tail=50

# 检查资源使用
free -h
df -h

# 重启服务
docker-compose -f deployment/docker_compose/docker-compose.main.yml restart
```

---

**文档版本**: v1.0
**最后更新**: 2025-08-14  
**适用版本**: Onyx v1.0+  
**服务器配置**: openEuler 20.03 LTS 双服务器
