#!/bin/bash

set -e

echo "🔧 Supervisord修复工具"
echo "======================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 1. 停止现有进程
echo ""
log_info "步骤1: 停止现有进程"
echo "----------------------------------------"

log_info "停止现有的supervisord进程..."
docker exec km-background pkill -f supervisord || log_info "没有运行中的supervisord进程"

log_info "停止现有的celery进程..."
docker exec km-background pkill -f celery || log_info "没有运行中的celery进程"

sleep 5

# 2. 检查和创建必要目录
echo ""
log_info "步骤2: 检查和创建必要目录"
echo "----------------------------------------"

log_info "创建日志目录..."
docker exec km-background mkdir -p /var/log
docker exec km-background chmod 755 /var/log

log_info "创建supervisor目录..."
docker exec km-background mkdir -p /etc/supervisor/conf.d
docker exec km-background chmod 755 /etc/supervisor/conf.d

# 3. 复制配置文件到正确位置
echo ""
log_info "步骤3: 配置supervisord"
echo "----------------------------------------"

log_info "复制supervisord配置文件..."
docker exec km-background cp /app/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

log_info "验证配置文件..."
if docker exec km-background test -f /etc/supervisor/conf.d/supervisord.conf; then
    log_success "配置文件复制成功"
else
    log_error "配置文件复制失败"
    exit 1
fi

# 4. 启动supervisord
echo ""
log_info "步骤4: 启动supervisord"
echo "----------------------------------------"

log_info "启动supervisord进程..."
docker exec -d km-background supervisord -c /etc/supervisor/conf.d/supervisord.conf

log_info "等待supervisord启动..."
sleep 10

# 验证supervisord启动
if docker exec km-background ps aux | grep -q supervisord; then
    log_success "supervisord启动成功"
else
    log_error "supervisord启动失败"
    
    log_info "查看启动错误..."
    docker exec km-background cat /var/log/supervisord.log 2>/dev/null || log_warning "无法读取supervisord日志"
    exit 1
fi

# 5. 检查worker状态
echo ""
log_info "步骤5: 检查worker状态"
echo "----------------------------------------"

log_info "等待worker启动..."
sleep 15

log_info "使用supervisorctl检查状态..."
docker exec km-background supervisorctl status

# 6. 启动未运行的worker
echo ""
log_info "步骤6: 启动未运行的worker"
echo "----------------------------------------"

log_info "启动所有程序..."
docker exec km-background supervisorctl start all

sleep 10

log_info "再次检查状态..."
docker exec km-background supervisorctl status

# 7. 验证Celery连接
echo ""
log_info "步骤7: 验证Celery连接"
echo "----------------------------------------"

log_info "等待所有worker完全启动..."
sleep 20

log_info "测试Celery worker连接..."
docker exec km-background timeout 15 python3 -c "
import sys
sys.path.append('/app')

try:
    from onyx.background.celery.versioned_apps.primary import celery_app
    
    print('测试Celery连接...')
    
    # 测试inspect功能
    inspect = celery_app.control.inspect(timeout=10)
    
    # 检查活跃worker
    active = inspect.active()
    if active:
        print(f'✅ 活跃worker: {list(active.keys())}')
        for worker, tasks in active.items():
            print(f'  {worker}: {len(tasks)} 个活跃任务')
    else:
        print('❌ 没有活跃worker')
    
    # 检查注册的worker
    registered = inspect.registered()
    if registered:
        print(f'✅ 注册的worker: {list(registered.keys())}')
        for worker, tasks in registered.items():
            indexing_tasks = [t for t in tasks if 'indexing' in t.lower() or 'docfetching' in t.lower()]
            if indexing_tasks:
                print(f'  {worker} 索引相关任务: {len(indexing_tasks)}')
    else:
        print('❌ 没有注册的worker')
    
    # 检查worker统计
    stats = inspect.stats()
    if stats:
        print(f'✅ Worker统计信息可用: {len(stats)} 个worker')
    else:
        print('❌ 无法获取worker统计信息')
        
except Exception as e:
    print(f'❌ Celery连接测试失败: {e}')
    import traceback
    traceback.print_exc()
" || log_warning "Celery连接测试失败或超时"

# 8. 发送测试任务
echo ""
log_info "步骤8: 发送测试任务"
echo "----------------------------------------"

log_info "发送索引检查任务..."
docker exec km-background python3 -c "
import sys
sys.path.append('/app')

try:
    from onyx.background.celery.versioned_apps.primary import celery_app
    from onyx.configs.constants import OnyxCeleryTask, OnyxCeleryQueues
    
    # 发送check_for_indexing任务
    result = celery_app.send_task(
        OnyxCeleryTask.CHECK_FOR_INDEXING,
        kwargs={'tenant_id': 'default'},
        queue=OnyxCeleryQueues.CELERY_PRIMARY
    )
    
    print(f'✅ 索引检查任务已发送: {result.id}')
    
except Exception as e:
    print(f'❌ 任务发送失败: {e}')
" || log_warning "测试任务发送失败"

# 9. 检查日志
echo ""
log_info "步骤9: 检查关键日志"
echo "----------------------------------------"

log_info "检查supervisord日志..."
if docker exec km-background test -f /var/log/supervisord.log; then
    echo "Supervisord日志（最近10行）:"
    docker exec km-background tail -10 /var/log/supervisord.log
fi

log_info "检查docfetching worker日志..."
if docker exec km-background test -f /var/log/celery_worker_docfetching.log; then
    echo "Docfetching Worker日志（最近5行）:"
    docker exec km-background tail -5 /var/log/celery_worker_docfetching.log
fi

log_info "检查primary worker日志..."
if docker exec km-background test -f /var/log/celery_worker_primary.log; then
    echo "Primary Worker日志（最近5行）:"
    docker exec km-background tail -5 /var/log/celery_worker_primary.log
fi

# 10. 生成修复报告
echo ""
log_info "步骤10: 生成修复报告"
echo "----------------------------------------"

echo ""
log_success "Supervisord修复完成！"
echo ""
echo "📋 修复总结:"
echo "1. ✅ 停止了现有进程"
echo "2. ✅ 创建了必要目录"
echo "3. ✅ 配置了supervisord"
echo "4. ✅ 启动了supervisord"
echo "5. ✅ 启动了所有worker"
echo "6. ✅ 验证了Celery连接"
echo "7. ✅ 发送了测试任务"

echo ""
echo "🔍 验证命令:"
echo "# 检查supervisord状态"
echo "docker exec km-background supervisorctl status"
echo ""
echo "# 检查Celery worker"
echo "docker exec km-background celery -A onyx.background.celery.versioned_apps.primary inspect active"
echo ""
echo "# 查看实时日志"
echo "docker logs km-background -f"
echo ""
echo "# 重启特定worker"
echo "docker exec km-background supervisorctl restart celery_worker_docfetching"

echo ""
echo "📱 监控建议:"
echo "1. 定期检查supervisorctl status"
echo "2. 监控/var/log/下的日志文件"
echo "3. 观察索引任务是否从Scheduled变为Running"
echo "4. 如果worker异常，使用supervisorctl restart重启"

echo ""
echo "🚨 重要提醒:"
echo "如果容器重启，supervisord需要重新启动"
echo "建议在Docker容器启动脚本中添加supervisord自动启动"
