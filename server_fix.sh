#!/bin/bash
# Onyx服务器修复脚本 - 适用于服务器10.0.83.30

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}✓${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}⚠${NC} $1"
}

log_error() {
    echo -e "${RED}✗${NC} $1"
}

log_section() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

echo "=== Onyx服务器修复脚本 ==="
echo "服务器: 10.0.83.30"
echo "时间: $(date)"

# 检查Docker Compose文件
if [ -f "docker-compose.main.yml" ]; then
    COMPOSE_FILE="docker-compose.main.yml"
else
    log_error "未找到docker-compose.main.yml文件"
    exit 1
fi

# 修复选项菜单
show_menu() {
    echo ""
    echo "请选择修复操作:"
    echo "1. 重启所有服务"
    echo "2. 重启Vespa服务"
    echo "3. 清理Redis锁"
    echo "4. 重启Redis服务"
    echo "5. 重启PostgreSQL服务"
    echo "6. 查看实时日志"
    echo "7. 完全重建服务 (危险操作)"
    echo "8. 检查并修复NOT_STARTED索引尝试"
    echo "9. 手动触发索引任务"
    echo "0. 退出"
    echo ""
}

# 重启所有服务
restart_all_services() {
    log_section "重启所有服务"
    docker compose -f $COMPOSE_FILE restart
    log_info "所有服务已重启"
}

# 重启Vespa服务
restart_vespa() {
    log_section "重启Vespa服务"
    VESPA_CONTAINERS=$(docker ps -a --filter "name=vespa" --format "{{.Names}}" || true)
    if [ -n "$VESPA_CONTAINERS" ]; then
        for container in $VESPA_CONTAINERS; do
            echo "重启容器: $container"
            docker restart $container
        done
        log_info "Vespa服务已重启"
        
        # 等待Vespa启动
        echo "等待Vespa启动..."
        sleep 10
        
        # 检查Vespa状态
        for container in $VESPA_CONTAINERS; do
            if docker exec $container curl -s http://localhost:8080/ApplicationStatus > /dev/null 2>&1; then
                log_info "Vespa服务正常响应"
            else
                log_warn "Vespa服务可能还在启动中"
            fi
        done
    else
        log_error "未找到Vespa容器"
    fi
}

# 清理Redis锁
clear_redis_locks() {
    log_section "清理Redis锁"
    REDIS_CONTAINERS=$(docker ps --filter "name=redis" --format "{{.Names}}" || true)
    if [ -n "$REDIS_CONTAINERS" ]; then
        for container in $REDIS_CONTAINERS; do
            echo "检查容器 $container 中的锁..."
            lock_keys=$(docker exec $container redis-cli --scan --pattern "*lock*" || true)
            if [ -n "$lock_keys" ]; then
                echo "发现的锁:"
                echo "$lock_keys"
                echo ""
                read -p "确认删除这些锁? (y/N): " confirm
                if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
                    echo "$lock_keys" | while read key; do
                        if [ -n "$key" ]; then
                            docker exec $container redis-cli del "$key"
                            echo "已删除锁: $key"
                        fi
                    done
                    log_info "Redis锁已清理"
                else
                    echo "取消清理操作"
                fi
            else
                log_info "没有发现锁"
            fi
        done
    else
        log_error "未找到运行中的Redis容器"
    fi
}

# 重启Redis服务
restart_redis() {
    log_section "重启Redis服务"
    REDIS_CONTAINERS=$(docker ps -a --filter "name=redis" --format "{{.Names}}" || true)
    if [ -n "$REDIS_CONTAINERS" ]; then
        for container in $REDIS_CONTAINERS; do
            echo "重启容器: $container"
            docker restart $container
        done
        log_info "Redis服务已重启"
    else
        log_error "未找到Redis容器"
    fi
}

# 重启PostgreSQL服务
restart_postgres() {
    log_section "重启PostgreSQL服务"
    POSTGRES_CONTAINERS=$(docker ps -a --filter "name=postgres" --format "{{.Names}}" || true)
    if [ -n "$POSTGRES_CONTAINERS" ]; then
        for container in $POSTGRES_CONTAINERS; do
            echo "重启容器: $container"
            docker restart $container
        done
        log_info "PostgreSQL服务已重启"
    else
        log_error "未找到PostgreSQL容器"
    fi
}

# 查看实时日志
view_logs() {
    log_section "查看实时日志"
    echo "按Ctrl+C退出日志查看"
    docker compose -f $COMPOSE_FILE logs -f
}

# 完全重建服务
rebuild_services() {
    log_section "完全重建服务 (危险操作)"
    echo "警告: 这将删除所有容器和数据卷!"
    echo "确保你已经备份了重要数据!"
    echo ""
    read -p "确认继续? 输入 'YES' 确认: " confirm
    if [ "$confirm" = "YES" ]; then
        echo "停止并删除所有容器..."
        docker compose -f $COMPOSE_FILE down -v
        
        echo "重新构建镜像..."
        docker compose -f $COMPOSE_FILE build --no-cache
        
        echo "启动服务..."
        docker compose -f $COMPOSE_FILE up -d
        
        log_info "服务重建完成"
    else
        echo "取消重建操作"
    fi
}

# 检查并修复NOT_STARTED索引尝试
fix_not_started_attempts() {
    log_section "检查NOT_STARTED索引尝试"
    echo "这个功能需要连接到数据库..."
    echo "请手动运行以下SQL查询来检查:"
    echo ""
    echo "SELECT ia.id, ia.connector_credential_pair_id, ia.status, ia.time_created"
    echo "FROM index_attempt ia"
    echo "WHERE ia.status = 'NOT_STARTED'"
    echo "ORDER BY ia.time_created DESC;"
    echo ""
    echo "如果发现长时间停留在NOT_STARTED状态的记录，可以考虑:"
    echo "1. 重启相关服务"
    echo "2. 清理Redis锁"
    echo "3. 手动触发索引任务"
}

# 手动触发索引任务
trigger_indexing() {
    log_section "手动触发索引任务"
    echo "查找Onyx应用容器..."
    ONYX_CONTAINERS=$(docker ps --filter "name=onyx" --format "{{.Names}}" || true)
    if [ -n "$ONYX_CONTAINERS" ]; then
        echo "找到Onyx容器:"
        echo "$ONYX_CONTAINERS"
        echo ""
        echo "可以尝试进入容器手动执行索引任务:"
        echo "docker exec -it <container_name> python -m onyx.background.celery.tasks.docprocessing.tasks"
        echo ""
        echo "或者重启celery worker:"
        for container in $ONYX_CONTAINERS; do
            if [[ $container == *"worker"* ]] || [[ $container == *"celery"* ]]; then
                echo "重启 $container..."
                docker restart $container
            fi
        done
    else
        log_error "未找到Onyx容器"
    fi
}

# 主循环
while true; do
    show_menu
    read -p "请选择 (0-9): " choice
    
    case $choice in
        1) restart_all_services ;;
        2) restart_vespa ;;
        3) clear_redis_locks ;;
        4) restart_redis ;;
        5) restart_postgres ;;
        6) view_logs ;;
        7) rebuild_services ;;
        8) fix_not_started_attempts ;;
        9) trigger_indexing ;;
        0) 
            echo "退出"
            exit 0
            ;;
        *)
            log_error "无效选择"
            ;;
    esac
    
    echo ""
    read -p "按Enter继续..."
done
