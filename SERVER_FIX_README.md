# Onyx服务器问题诊断和修复指南

## 服务器环境
- 服务器IP: **********
- 操作系统: Linux
- 部署方式: Docker Compose

## 问题描述
根据日志分析，主要问题包括：
1. **Vespa服务问题**: Readiness probe超时
2. **Redis锁竞争**: Primary worker lock无法获取
3. **索引任务执行异常**: check_for_indexing任务运行但跳过NOT_STARTED记录

## 快速修复步骤

### 1. 上传脚本到服务器
将以下文件上传到服务器的项目目录：
- `quick_fix.sh` - 快速修复脚本
- `server_diagnose.sh` - 完整诊断脚本
- `server_fix.sh` - 交互式修复脚本
- `diagnose_indexing_issue.py` - Python诊断脚本

### 2. 设置执行权限
```bash
chmod +x quick_fix.sh server_diagnose.sh server_fix.sh
```

### 3. 执行快速修复
```bash
# 快速修复 (推荐首先执行)
./quick_fix.sh

# 或者完整诊断
./server_diagnose.sh

# 或者交互式修复
./server_fix.sh
```

## 脚本说明

### quick_fix.sh
**用途**: 针对当前问题的快速修复
**操作**:
1. 重启Vespa服务
2. 清理Redis锁
3. 重启Celery Worker
4. 检查服务状态

### server_diagnose.sh
**用途**: 全面诊断系统状态
**检查项目**:
- Docker服务状态
- 容器运行状态
- 服务健康检查
- 系统资源使用
- 网络端口状态

### server_fix.sh
**用途**: 交互式修复工具
**功能菜单**:
1. 重启所有服务
2. 重启Vespa服务
3. 清理Redis锁
4. 重启Redis服务
5. 重启PostgreSQL服务
6. 查看实时日志
7. 完全重建服务
8. 检查NOT_STARTED索引尝试
9. 手动触发索引任务

### diagnose_indexing_issue.py
**用途**: Python诊断脚本
**功能**:
- 检查Redis连接和锁状态
- 检查PostgreSQL数据库状态
- 检查Vespa服务状态
- 分析索引尝试记录

## 手动修复步骤

如果脚本无法解决问题，可以手动执行以下步骤：

### 1. 检查容器状态
```bash
docker ps -a
docker compose -f docker-compose.main.yml ps
```

### 2. 重启Vespa服务
```bash
# 找到Vespa容器
docker ps | grep vespa

# 重启Vespa容器
docker restart <vespa_container_name>

# 检查Vespa健康状态
docker exec <vespa_container_name> curl http://localhost:8080/ApplicationStatus
```

### 3. 清理Redis锁
```bash
# 进入Redis容器
docker exec -it <redis_container_name> redis-cli

# 查看所有锁
KEYS *lock*

# 删除所有锁 (谨慎操作)
FLUSHDB

# 或者删除特定锁
DEL <lock_key>
```

### 4. 检查数据库状态
```bash
# 进入PostgreSQL容器
docker exec -it <postgres_container_name> psql -U postgres -d onyx

# 查看NOT_STARTED状态的索引尝试
SELECT ia.id, ia.connector_credential_pair_id, ia.status, ia.time_created, c.name
FROM index_attempt ia
JOIN connector_credential_pair cc ON ia.connector_credential_pair_id = cc.id
JOIN connector c ON cc.connector_id = c.id
WHERE ia.status = 'NOT_STARTED'
ORDER BY ia.time_created DESC;

# 查看连接器状态
SELECT cc.id, c.name, cc.status, c.source
FROM connector_credential_pair cc
JOIN connector c ON cc.connector_id = c.id;
```

### 5. 重启相关服务
```bash
# 重启所有服务
docker compose -f docker-compose.main.yml restart

# 或者重启特定服务
docker restart <container_name>
```

## 监控和预防

### 1. 设置监控
```bash
# 实时查看日志
docker compose -f docker-compose.main.yml logs -f

# 监控特定服务
docker logs -f <container_name>
```

### 2. 定期检查
- 每天检查容器状态
- 监控系统资源使用
- 定期清理Redis锁
- 检查Vespa服务健康状态

### 3. 预防措施
- 确保服务器有足够的内存和CPU资源
- 定期备份数据
- 监控磁盘空间使用
- 设置合理的锁超时时间

## 常见问题解决

### Q: Vespa服务一直无法启动
A: 
1. 检查内存使用情况: `free -h`
2. 检查磁盘空间: `df -h`
3. 查看Vespa日志: `docker logs <vespa_container>`
4. 考虑增加内存分配或重启服务器

### Q: Redis锁一直存在
A:
1. 检查是否有僵死的进程
2. 重启Redis服务
3. 检查应用程序是否正确释放锁

### Q: 索引任务不执行
A:
1. 检查Celery Worker状态
2. 重启Worker服务
3. 检查数据库连接
4. 验证连接器配置

## 联系支持
如果问题仍然存在，请提供：
1. 脚本执行结果
2. 相关日志文件
3. 系统资源使用情况
4. 错误信息截图
