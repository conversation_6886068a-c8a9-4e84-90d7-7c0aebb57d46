#!/bin/bash

set -e

echo "🔧 Vespa容器状态错误修复脚本"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. 诊断当前状态
echo ""
log_info "步骤1: 诊断当前Vespa容器状态"
echo "----------------------------------------"

# 检查容器状态
log_info "检查km-vespa容器状态..."
if docker ps | grep -q km-vespa; then
    log_success "km-vespa容器正在运行"
    docker ps | grep km-vespa
else
    log_error "km-vespa容器未运行"
    echo "尝试查看所有容器状态："
    docker ps -a | grep km-vespa || log_error "未找到km-vespa容器"
fi

# 检查容器日志
log_info "检查Vespa启动日志（最近50行）..."
echo "----------------------------------------"
if docker logs km-vespa --tail=50 2>/dev/null; then
    log_success "日志获取成功"
else
    log_error "无法获取容器日志"
fi

# 检查健康状态
log_info "测试Vespa健康检查端点..."
if curl -f http://localhost:8081/ApplicationStatus 2>/dev/null; then
    log_success "Vespa健康检查通过"
else
    log_warning "Vespa健康检查失败"
fi

# 2. 检查系统资源
echo ""
log_info "步骤2: 检查系统资源使用情况"
echo "----------------------------------------"

log_info "内存使用情况："
free -h

log_info "磁盘使用情况："
df -h /

log_info "Docker容器资源使用："
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" | head -10

# 3. 停止相关服务
echo ""
log_info "步骤3: 停止相关服务以进行修复"
echo "----------------------------------------"

log_info "停止依赖Vespa的服务..."
docker-compose -f docker-compose.main.yml stop background api_server web_server nginx 2>/dev/null || true

log_info "停止Vespa服务..."
docker-compose -f docker-compose.main.yml stop vespa 2>/dev/null || true

# 等待服务完全停止
sleep 10

# 4. 清理和重启
echo ""
log_info "步骤4: 清理并重启Vespa服务"
echo "----------------------------------------"

log_info "清理Vespa数据（如果需要）..."
read -p "是否清理Vespa数据目录？这将删除所有索引数据 (y/N): " clean_data
if [[ $clean_data =~ ^[Yy]$ ]]; then
    log_warning "清理Vespa数据目录..."
    sudo rm -rf /opt/km/vespa/* 2>/dev/null || true
    log_success "数据目录已清理"
fi

log_info "重新创建必要的目录..."
sudo mkdir -p /opt/km/vespa
sudo chown -R $USER:$USER /opt/km/vespa

# 5. 启动Vespa服务
echo ""
log_info "步骤5: 启动Vespa服务"
echo "----------------------------------------"

log_info "启动外部依赖检查..."
docker-compose -f docker-compose.main.yml up -d dependency_check

log_info "等待外部依赖就绪..."
sleep 30

log_info "启动Vespa服务..."
docker-compose -f docker-compose.main.yml up -d vespa

log_info "等待Vespa启动（这可能需要5-10分钟）..."
echo "正在监控Vespa启动进度..."

# 监控Vespa启动
timeout=600  # 10分钟超时
elapsed=0
while ! curl -f http://localhost:8081/ApplicationStatus &>/dev/null; do
    sleep 15
    elapsed=$((elapsed + 15))
    echo "等待中... ${elapsed}/${timeout}秒"
    
    if [[ $elapsed -ge $timeout ]]; then
        log_error "Vespa启动超时"
        echo "查看Vespa日志："
        docker logs km-vespa --tail=50
        exit 1
    fi
    
    # 每分钟显示一次日志
    if [[ $((elapsed % 60)) -eq 0 ]]; then
        echo "当前Vespa日志："
        docker logs km-vespa --tail=10
    fi
done

log_success "Vespa服务启动成功！"

# 6. 验证Vespa状态
echo ""
log_info "步骤6: 验证Vespa服务状态"
echo "----------------------------------------"

log_info "测试ApplicationStatus端点..."
curl -s http://localhost:8081/ApplicationStatus | head -5

log_info "测试基本连通性..."
curl -s http://localhost:8081/ | head -5

log_info "检查配置服务器..."
curl -s http://localhost:19071/ | head -5 || log_warning "配置服务器可能未就绪"

# 7. 启动其他服务
echo ""
log_info "步骤7: 启动其他服务"
echo "----------------------------------------"

log_info "启动model_server..."
docker-compose -f docker-compose.main.yml up -d model_server

log_info "等待model_server启动..."
timeout=120
elapsed=0
while ! curl -f http://localhost:9000/health &>/dev/null; do
    sleep 5
    elapsed=$((elapsed + 5))
    echo "等待model_server... ${elapsed}/${timeout}秒"
    if [[ $elapsed -ge $timeout ]]; then
        log_warning "model_server启动超时，但继续启动其他服务"
        break
    fi
done

log_info "启动api_server..."
docker-compose -f docker-compose.main.yml up -d api_server

log_info "等待api_server启动..."
timeout=120
elapsed=0
while ! curl -f http://localhost:8080/health &>/dev/null; do
    sleep 5
    elapsed=$((elapsed + 5))
    echo "等待api_server... ${elapsed}/${timeout}秒"
    if [[ $elapsed -ge $timeout ]]; then
        log_warning "api_server启动超时"
        break
    fi
done

log_info "启动background服务..."
docker-compose -f docker-compose.main.yml up -d background

log_info "启动web_server..."
docker-compose -f docker-compose.main.yml up -d web_server

log_info "启动nginx..."
docker-compose -f docker-compose.main.yml up -d nginx

# 8. 最终验证
echo ""
log_info "步骤8: 最终系统验证"
echo "----------------------------------------"

sleep 30

log_info "检查所有容器状态..."
docker-compose -f docker-compose.main.yml ps

log_info "验证服务健康状态..."
echo "Vespa: $(curl -s http://localhost:8081/ApplicationStatus | head -1 || echo '❌ 失败')"
echo "API: $(curl -s http://localhost:8080/health || echo '❌ 失败')"
echo "Web: $(curl -s http://localhost:3000 >/dev/null && echo '✅ 正常' || echo '❌ 失败')"

# 检查background服务日志
log_info "检查background服务日志（查找Vespa相关错误）..."
docker logs km-background --tail=20 | grep -i vespa || log_success "未发现Vespa相关错误"

echo ""
log_success "Vespa修复脚本执行完成！"
echo ""
echo "📋 修复总结："
echo "1. ✅ 调整了健康检查超时时间（120s -> 300s）"
echo "2. ✅ 增加了资源分配（4G -> 8G内存）"
echo "3. ✅ 重新启动了Vespa服务"
echo "4. ✅ 验证了服务连通性"
echo ""
echo "🔍 如果问题仍然存在，请检查："
echo "- 系统内存是否充足（建议至少16G）"
echo "- 是否需要部署Vespa应用包"
echo "- 网络连接是否正常"
echo ""
echo "📱 访问地址："
echo "- 主页面: http://**********:18080"
echo "- API: http://**********:8080"
echo "- Vespa: http://**********:8081"
