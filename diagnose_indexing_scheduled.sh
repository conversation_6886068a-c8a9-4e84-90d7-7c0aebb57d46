#!/bin/bash

set -e

echo "🔍 诊断Indexing Attempts处于Scheduled状态问题"
echo "=================================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 1. 检查容器状态
echo ""
log_info "步骤1: 检查相关容器状态"
echo "----------------------------------------"

log_info "检查所有容器状态..."
docker compose -f docker-compose.main.yml ps

echo ""
log_info "检查background容器详细状态..."
if docker ps | grep -q km-background; then
    log_success "km-background容器正在运行"
    # 检查容器状态，避免健康检查不存在的问题
    CONTAINER_STATUS=$(docker inspect km-background --format='{{.State.Status}}' 2>/dev/null || echo "unknown")
    HEALTH_STATUS=$(docker inspect km-background --format='{{if .State.Health}}{{.State.Health.Status}}{{else}}no-healthcheck{{end}}' 2>/dev/null || echo "unknown")
    echo "容器状态: $CONTAINER_STATUS, 健康状态: $HEALTH_STATUS"
else
    log_error "km-background容器未运行"
fi

# 2. 检查Celery worker状态
echo ""
log_info "步骤2: 检查Celery worker状态"
echo "----------------------------------------"

log_info "检查Celery worker进程..."
docker exec km-background ps aux | grep celery || log_warning "未找到celery进程"

echo ""
log_info "检查Celery worker日志（最近50行）..."
docker logs km-background --tail=50 | grep -i celery || log_warning "未找到celery相关日志"

echo ""
log_info "检查docfetching相关日志..."
docker logs km-background --tail=100 | grep -i "docfetching\|indexing\|check_for_indexing" || log_warning "未找到索引相关日志"

# 3. 检查Redis连接
echo ""
log_info "步骤3: 检查Redis连接状态"
echo "----------------------------------------"

log_info "检查Redis服务状态（位于**********）..."
# Redis在数据库服务器**********上，不在本地容器中
log_info "测试Redis连接..."
if docker exec km-background python3 -c "
import redis
try:
    r = redis.Redis(host='**********', port=6379, db=0)
    r.ping()
    print('Redis连接正常')
except Exception as e:
    print(f'Redis连接失败: {e}')
" 2>/dev/null; then
    log_success "Redis连接测试通过"
else
    log_error "Redis连接测试失败"
    log_info "尝试从主机直接测试Redis连接..."
    if redis-cli -h ********** -p 6379 ping 2>/dev/null; then
        log_warning "主机可以连接Redis，但容器内连接失败"
    else
        log_error "主机也无法连接Redis"
    fi
fi

# 4. 检查数据库中的索引任务状态
echo ""
log_info "步骤4: 检查数据库中的索引任务状态"
echo "----------------------------------------"

log_info "查询当前索引任务状态..."
docker exec km-background python3 -c "
import os
import sys
sys.path.append('/app')

from onyx.db.engine.sql_engine import get_session_with_current_tenant
from onyx.db.models import IndexAttempt, ConnectorCredentialPair, Connector
from sqlalchemy import desc

try:
    with get_session_with_current_tenant() as db_session:
        # 查询最近的索引任务
        recent_attempts = db_session.query(IndexAttempt).join(
            ConnectorCredentialPair, IndexAttempt.connector_credential_pair_id == ConnectorCredentialPair.id
        ).join(
            Connector, ConnectorCredentialPair.connector_id == Connector.id
        ).order_by(desc(IndexAttempt.time_created)).limit(10).all()
        
        print('最近的索引任务:')
        print('ID\\t状态\\t\\t连接器\\t\\t开始时间\\t\\tCelery任务ID')
        print('-' * 80)
        for attempt in recent_attempts:
            connector_name = attempt.connector_credential_pair.connector.name
            start_time = attempt.time_started.strftime('%Y-%m-%d %H:%M:%S') if attempt.time_started else 'None'
            celery_id = attempt.celery_task_id or 'None'
            print(f'{attempt.id}\\t{attempt.status.value}\\t{connector_name[:15]}\\t{start_time}\\t{celery_id[:20]}')
            
        # 统计各状态的任务数量
        from onyx.db.enums import IndexingStatus
        from sqlalchemy import func
        
        status_counts = db_session.query(
            IndexAttempt.status, func.count(IndexAttempt.id)
        ).group_by(IndexAttempt.status).all()
        
        print('\\n索引任务状态统计:')
        for status, count in status_counts:
            print(f'{status.value}: {count}')
            
except Exception as e:
    print(f'查询数据库失败: {e}')
" 2>/dev/null || log_error "数据库查询失败"

# 5. 检查Celery任务队列
echo ""
log_info "步骤5: 检查Celery任务队列状态"
echo "----------------------------------------"

log_info "检查Celery队列状态..."
docker exec km-background python3 -c "
import os
import sys
sys.path.append('/app')

try:
    from celery import Celery
    from onyx.configs.app_configs import REDIS_HOST, REDIS_PORT, REDIS_DB_NUMBER_CELERY
    
    # 创建Celery应用
    broker_url = f'redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB_NUMBER_CELERY}'
    app = Celery('onyx', broker=broker_url)
    
    # 检查队列状态
    inspect = app.control.inspect()
    
    # 检查活跃任务
    active_tasks = inspect.active()
    if active_tasks:
        print('活跃任务:')
        for worker, tasks in active_tasks.items():
            print(f'Worker {worker}: {len(tasks)} 个任务')
            for task in tasks[:3]:  # 只显示前3个
                print(f'  - {task.get(\"name\", \"Unknown\")}: {task.get(\"id\", \"No ID\")}')
    else:
        print('没有活跃任务')
    
    # 检查预定任务
    scheduled_tasks = inspect.scheduled()
    if scheduled_tasks:
        print('\\n预定任务:')
        for worker, tasks in scheduled_tasks.items():
            print(f'Worker {worker}: {len(tasks)} 个任务')
    else:
        print('\\n没有预定任务')
        
    # 检查注册的任务
    registered_tasks = inspect.registered()
    if registered_tasks:
        print('\\n注册的任务类型:')
        for worker, tasks in registered_tasks.items():
            indexing_tasks = [t for t in tasks if 'indexing' in t.lower() or 'docfetching' in t.lower()]
            if indexing_tasks:
                print(f'Worker {worker} 索引相关任务:')
                for task in indexing_tasks:
                    print(f'  - {task}')
    
except Exception as e:
    print(f'Celery检查失败: {e}')
" 2>/dev/null || log_error "Celery队列检查失败"

# 6. 检查配置文件
echo ""
log_info "步骤6: 检查关键配置"
echo "----------------------------------------"

log_info "检查环境变量配置..."
docker exec km-background env | grep -E "(REDIS|CELERY|POSTGRES)" | head -10

echo ""
log_info "检查Celery配置..."
docker exec km-background python3 -c "
import os
import sys
sys.path.append('/app')

try:
    from onyx.configs.app_configs import REDIS_HOST, REDIS_PORT, REDIS_DB_NUMBER_CELERY
    from onyx.configs.constants import OnyxCeleryQueues, OnyxCeleryTask
    
    print(f'Redis配置: {REDIS_HOST}:{REDIS_PORT}/{REDIS_DB_NUMBER_CELERY}')
    print(f'Celery队列: {[q.value for q in OnyxCeleryQueues]}')
    print(f'索引相关任务: {OnyxCeleryTask.CONNECTOR_DOC_FETCHING_TASK}')
    
except Exception as e:
    print(f'配置检查失败: {e}')
" 2>/dev/null || log_error "配置检查失败"

# 7. 生成诊断报告
echo ""
log_info "步骤7: 生成诊断报告"
echo "----------------------------------------"

echo ""
echo "📋 诊断总结:"
echo "1. 容器状态: $(docker ps | grep -q km-background && echo '✅ 正常' || echo '❌ 异常')"
echo "2. Redis连接: $(docker run --rm redis:6 redis-cli -h ********** -p 6379 ping 2>/dev/null && echo '✅ 正常' || echo '❌ 异常')"
echo "3. 数据库连接: $(docker run --rm -e PGPASSWORD="Sygy@2025" postgres:13 psql -h ********** -U km_user -d "knowledge-manage" -c "SELECT 1;" 2>/dev/null && echo '✅ 正常' || echo '❌ 异常')"

echo ""
echo "🔧 建议的修复步骤:"
echo "1. 如果Redis连接异常，检查**********服务器上的Redis服务"
echo "2. 如果Celery worker异常，重启background容器"
echo "3. 如果有卡住的任务，清理数据库中的NOT_STARTED状态任务"
echo "4. 检查系统资源是否充足"

echo ""
echo "📱 快速修复命令:"
echo "# 重启相关服务"
echo "docker compose -f docker-compose.main.yml restart background"
echo ""
echo "# 清理卡住的任务（谨慎使用）"
echo "./fix_scheduled_indexing.sh"
