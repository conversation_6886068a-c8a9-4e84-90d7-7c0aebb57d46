#!/bin/bash
# Onyx服务器诊断脚本 - 适用于服务器10.0.83.30

set -e

echo "=== Onyx服务器诊断脚本 ==="
echo "服务器: 10.0.83.30"
echo "时间: $(date)"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}✓${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}⚠${NC} $1"
}

log_error() {
    echo -e "${RED}✗${NC} $1"
}

log_section() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# 1. 检查Docker服务状态
log_section "检查Docker服务状态"
if systemctl is-active --quiet docker; then
    log_info "Docker服务正在运行"
    docker --version
else
    log_error "Docker服务未运行,请手动启动Docker服务"
    exit 1
fi

# 2. 检查Docker Compose文件
log_section "检查Docker Compose文件"
if [ -f "docker-compose.main.yml" ]; then
    log_info "找到 docker-compose.main.yml"
    COMPOSE_FILE="docker-compose.main.yml"
else
    log_error "未找到Docker Compose文件"
    exit 1
fi

# 3. 检查所有容器状态
log_section "检查容器状态"
echo "所有容器状态:"
docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# 4. 检查关键服务容器
log_section "检查关键服务"

# 检查Vespa容器
echo "检查Vespa容器:"
VESPA_CONTAINERS=$(docker ps -a --filter "name=vespa" --format "{{.Names}}" || true)
if [ -n "$VESPA_CONTAINERS" ]; then
    for container in $VESPA_CONTAINERS; do
        status=$(docker inspect --format='{{.State.Status}}' $container)
        echo "  - $container: $status"
        
        if [ "$status" = "running" ]; then
            echo "    检查Vespa健康状态..."
            if docker exec $container curl -s http://localhost:8080/ApplicationStatus > /dev/null 2>&1; then
                log_info "    Vespa响应正常"
            else
                log_error "    Vespa无响应"
            fi
        fi
    done
else
    log_warn "未找到Vespa容器"
fi

# 检查Redis容器
echo "检查Redis容器:"
REDIS_CONTAINERS=$(docker ps -a --filter "name=redis" --format "{{.Names}}" || true)
if [ -n "$REDIS_CONTAINERS" ]; then
    for container in $REDIS_CONTAINERS; do
        status=$(docker inspect --format='{{.State.Status}}' $container)
        echo "  - $container: $status"
        
        if [ "$status" = "running" ]; then
            echo "    检查Redis连接..."
            if docker exec $container redis-cli ping > /dev/null 2>&1; then
                log_info "    Redis响应正常"
                # 检查锁
                lock_count=$(docker exec $container redis-cli --scan --pattern "*lock*" | wc -l)
                if [ "$lock_count" -gt 0 ]; then
                    log_warn "    发现 $lock_count 个锁"
                else
                    log_info "    没有活跃的锁"
                fi
            else
                log_error "    Redis无响应"
            fi
        fi
    done
else
    log_warn "未找到Redis容器"
fi

# 检查PostgreSQL容器
echo "检查PostgreSQL容器:"
POSTGRES_CONTAINERS=$(docker ps -a --filter "name=postgres" --format "{{.Names}}" || true)
if [ -n "$POSTGRES_CONTAINERS" ]; then
    for container in $POSTGRES_CONTAINERS; do
        status=$(docker inspect --format='{{.State.Status}}' $container)
        echo "  - $container: $status"
    done
else
    log_warn "未找到PostgreSQL容器"
fi

# 5. 检查服务日志
log_section "检查最近的服务日志"
echo "最近的Docker Compose日志 (最后50行):"
docker compose -f $COMPOSE_FILE logs --tail=50 --timestamps

# 6. 检查系统资源
log_section "检查系统资源"
echo "内存使用情况:"
free -h

echo "磁盘使用情况:"
df -h

echo "CPU负载:"
uptime

# 7. 检查网络连接
log_section "检查网络连接"
echo "检查关键端口:"
netstat -tlnp | grep -E ":(8080|6379|5432|3000)" || echo "未找到监听的关键端口"

echo ""
log_section "诊断完成"
echo "如需修复，请运行: ./server_fix.sh"
