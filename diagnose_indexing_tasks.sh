#!/bin/bash

set -e

echo "🔍 索引任务深度诊断工具"
echo "========================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 1. 检查Celery worker详细状态
echo ""
log_info "步骤1: 检查Celery worker详细状态"
echo "----------------------------------------"

log_info "检查docfetching worker状态..."
docker exec km-background celery -A onyx.background.celery.versioned_apps.docfetching inspect active || log_warning "docfetching worker无响应"

log_info "检查primary worker状态..."
docker exec km-background celery -A onyx.background.celery.versioned_apps.primary inspect active || log_warning "primary worker无响应"

# 2. 检查队列状态
echo ""
log_info "步骤2: 检查Celery队列状态"
echo "----------------------------------------"

log_info "检查connector_doc_fetching队列..."
docker exec km-background python3 -c "
import sys
sys.path.append('/app')

try:
    from onyx.configs.app_configs import REDIS_HOST, REDIS_PORT, REDIS_DB_NUMBER_CELERY
    import redis
    
    r = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB_NUMBER_CELERY)
    
    # 检查队列长度
    queue_name = 'connector_doc_fetching'
    queue_length = r.llen(queue_name)
    print(f'队列 {queue_name} 长度: {queue_length}')
    
    # 检查其他相关队列
    queues = ['celery', 'vespa_metadata_sync', 'docprocessing']
    for queue in queues:
        length = r.llen(queue)
        print(f'队列 {queue} 长度: {length}')
    
    # 检查所有队列
    all_keys = r.keys('*')
    queue_keys = [k.decode() for k in all_keys if 'queue' in k.decode().lower() or k.decode() in queues]
    print(f'\\n所有队列相关键: {queue_keys}')
    
except Exception as e:
    print(f'队列检查失败: {e}')
" || log_error "队列状态检查失败"

# 3. 检查数据库中的索引任务
echo ""
log_info "步骤3: 检查数据库中的索引任务详情"
echo "----------------------------------------"

log_info "查询详细的索引任务信息..."
docker exec km-background python3 -c "
import sys
sys.path.append('/app')

try:
    from onyx.db.engine.sql_engine import get_session_with_current_tenant
    from onyx.db.models import IndexAttempt, ConnectorCredentialPair, Connector
    from onyx.db.enums import IndexingStatus
    from sqlalchemy import desc, and_
    from datetime import datetime, timedelta
    
    with get_session_with_current_tenant() as db_session:
        # 查询最近的NOT_STARTED任务
        recent_not_started = db_session.query(IndexAttempt).join(
            ConnectorCredentialPair, IndexAttempt.connector_credential_pair_id == ConnectorCredentialPair.id
        ).join(
            Connector, ConnectorCredentialPair.connector_id == Connector.id
        ).filter(
            IndexAttempt.status == IndexingStatus.NOT_STARTED
        ).order_by(desc(IndexAttempt.time_created)).limit(5).all()
        
        print('NOT_STARTED状态的索引任务:')
        print('ID\\t连接器\\t\\t创建时间\\t\\t\\tCelery任务ID')
        print('-' * 80)
        for attempt in recent_not_started:
            connector_name = attempt.connector_credential_pair.connector.name
            created_time = attempt.time_created.strftime('%Y-%m-%d %H:%M:%S') if attempt.time_created else 'None'
            celery_id = attempt.celery_task_id or 'None'
            print(f'{attempt.id}\\t{connector_name[:15]}\\t{created_time}\\t{celery_id}')
        
        # 检查是否有长时间卡住的任务
        cutoff_time = datetime.utcnow() - timedelta(hours=1)
        stuck_tasks = db_session.query(IndexAttempt).filter(
            and_(
                IndexAttempt.status == IndexingStatus.NOT_STARTED,
                IndexAttempt.time_created < cutoff_time
            )
        ).count()
        
        print(f'\\n超过1小时的NOT_STARTED任务数量: {stuck_tasks}')
        
        # 检查连接器状态
        from onyx.db.enums import ConnectorCredentialPairStatus
        active_connectors = db_session.query(ConnectorCredentialPair).filter(
            ConnectorCredentialPair.status == ConnectorCredentialPairStatus.ACTIVE
        ).count()
        
        print(f'活跃连接器数量: {active_connectors}')
        
except Exception as e:
    print(f'数据库查询失败: {e}')
    import traceback
    traceback.print_exc()
" || log_error "数据库查询失败"

# 4. 检查check_for_indexing任务
echo ""
log_info "步骤4: 检查check_for_indexing任务执行"
echo "----------------------------------------"

log_info "手动触发check_for_indexing任务..."
docker exec km-background python3 -c "
import sys
sys.path.append('/app')

try:
    from onyx.background.celery.versioned_apps.primary import celery_app
    from onyx.configs.constants import OnyxCeleryTask, OnyxCeleryQueues, OnyxCeleryPriority
    
    # 发送check_for_indexing任务
    result = celery_app.send_task(
        OnyxCeleryTask.CHECK_FOR_INDEXING,
        kwargs={'tenant_id': 'default'},
        queue=OnyxCeleryQueues.CELERY_PRIMARY,
        priority=OnyxCeleryPriority.HIGH
    )
    
    print(f'✅ check_for_indexing任务已发送: {result.id}')
    
    # 等待一下看结果
    import time
    time.sleep(10)
    
    if result.ready():
        print(f'✅ 任务已完成，结果: {result.result}')
    else:
        print(f'⏳ 任务处理中，状态: {result.state}')
        
except Exception as e:
    print(f'❌ 任务发送失败: {e}')
    import traceback
    traceback.print_exc()
" || log_warning "check_for_indexing任务测试失败"

# 5. 检查连接器配置
echo ""
log_info "步骤5: 检查连接器配置"
echo "----------------------------------------"

log_info "检查连接器和凭证对配置..."
docker exec km-background python3 -c "
import sys
sys.path.append('/app')

try:
    from onyx.db.engine.sql_engine import get_session_with_current_tenant
    from onyx.db.models import ConnectorCredentialPair, Connector
    from onyx.db.enums import ConnectorCredentialPairStatus
    
    with get_session_with_current_tenant() as db_session:
        # 查询所有连接器
        cc_pairs = db_session.query(ConnectorCredentialPair).join(
            Connector, ConnectorCredentialPair.connector_id == Connector.id
        ).all()
        
        print('连接器配置:')
        print('ID\\t连接器名称\\t\\t状态\\t\\t\\t刷新频率')
        print('-' * 70)
        for cc_pair in cc_pairs:
            connector_name = cc_pair.connector.name
            status = cc_pair.status.value
            refresh_freq = getattr(cc_pair.connector, 'refresh_freq', 'None')
            print(f'{cc_pair.id}\\t{connector_name[:15]}\\t{status}\\t{refresh_freq}')
        
        # 检查活跃的连接器
        active_pairs = [cc for cc in cc_pairs if cc.status == ConnectorCredentialPairStatus.ACTIVE]
        print(f'\\n活跃连接器数量: {len(active_pairs)}')
        
        # 检查是否有indexing_trigger
        triggered_pairs = [cc for cc in active_pairs if getattr(cc, 'indexing_trigger', None) is not None]
        print(f'有索引触发器的连接器: {len(triggered_pairs)}')
        
except Exception as e:
    print(f'连接器配置检查失败: {e}')
    import traceback
    traceback.print_exc()
" || log_error "连接器配置检查失败"

# 6. 检查日志中的索引相关信息
echo ""
log_info "步骤6: 检查日志中的索引信息"
echo "----------------------------------------"

log_info "检查docfetching worker日志..."
if docker exec km-background test -f /var/log/celery_worker_docfetching.log; then
    echo "Docfetching Worker最近日志:"
    docker exec km-background tail -20 /var/log/celery_worker_docfetching.log | grep -E "(indexing|task|error)" || echo "未找到相关日志"
fi

log_info "检查primary worker日志..."
if docker exec km-background test -f /var/log/celery_worker_primary.log; then
    echo "Primary Worker最近日志:"
    docker exec km-background tail -20 /var/log/celery_worker_primary.log | grep -E "(indexing|check_for_indexing|task)" || echo "未找到相关日志"
fi

log_info "检查beat日志..."
if docker exec km-background test -f /var/log/celery_beat.log; then
    echo "Celery Beat最近日志:"
    docker exec km-background tail -10 /var/log/celery_beat.log | grep -E "(check_for_indexing|beat)" || echo "未找到相关日志"
fi

# 7. 生成诊断报告
echo ""
log_info "步骤7: 生成诊断报告"
echo "----------------------------------------"

echo ""
echo "📋 索引任务诊断报告:"
echo "1. Supervisord状态: ✅ 所有worker正常运行"
echo "2. Celery连接: $(docker exec km-background celery -A onyx.background.celery.versioned_apps.docfetching inspect ping 2>/dev/null | grep -q pong && echo '✅ 正常' || echo '❌ 异常')"
echo "3. 队列状态: $(docker exec km-background python3 -c 'import sys; sys.path.append(\"/app\"); from onyx.configs.app_configs import REDIS_HOST, REDIS_PORT, REDIS_DB_NUMBER_CELERY; import redis; r = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB_NUMBER_CELERY); print(r.llen(\"connector_doc_fetching\"))' 2>/dev/null || echo '检查失败')"

echo ""
echo "🔧 下一步诊断建议:"
echo "1. 如果队列中有任务但未处理，检查worker日志"
echo "2. 如果没有任务进入队列，检查check_for_indexing逻辑"
echo "3. 如果连接器配置有问题，检查连接器状态和权限"
echo "4. 手动触发索引任务进行测试"

echo ""
echo "📱 手动测试命令:"
echo "# 手动触发索引检查"
echo "docker exec km-background python3 -c \"import sys; sys.path.append('/app'); from onyx.background.celery.versioned_apps.primary import celery_app; from onyx.configs.constants import OnyxCeleryTask; celery_app.send_task(OnyxCeleryTask.CHECK_FOR_INDEXING, kwargs={'tenant_id': 'default'})\""
echo ""
echo "# 查看实时日志"
echo "docker logs km-background -f | grep -E '(indexing|docfetching|task)'"
