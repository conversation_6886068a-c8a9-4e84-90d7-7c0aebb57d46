#!/bin/bash

set -e

echo "🔍 Supervisord状态检查工具"
echo "=========================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 1. 检查supervisord进程
echo ""
log_info "步骤1: 检查supervisord进程状态"
echo "----------------------------------------"

log_info "查找supervisord进程..."
if docker exec km-background ps aux | grep -q supervisord; then
    log_success "supervisord进程正在运行"
    docker exec km-background ps aux | grep supervisord | grep -v grep
else
    log_error "supervisord进程未运行"
fi

# 2. 检查supervisord配置文件
echo ""
log_info "步骤2: 检查supervisord配置文件"
echo "----------------------------------------"

log_info "检查配置文件位置..."
docker exec km-background find /app -name "supervisord.conf" -o -name "*.conf" | grep -E "(supervisor|celery)" || log_warning "未找到配置文件"

log_info "检查/etc/supervisor目录..."
docker exec km-background ls -la /etc/supervisor/ 2>/dev/null || log_warning "/etc/supervisor目录不存在"

log_info "检查/etc/supervisor/conf.d目录..."
docker exec km-background ls -la /etc/supervisor/conf.d/ 2>/dev/null || log_warning "/etc/supervisor/conf.d目录不存在"

# 3. 使用supervisorctl检查状态
echo ""
log_info "步骤3: 使用supervisorctl检查服务状态"
echo "----------------------------------------"

log_info "检查supervisorctl是否可用..."
if docker exec km-background which supervisorctl >/dev/null 2>&1; then
    log_success "supervisorctl可用"
    
    log_info "获取所有程序状态..."
    docker exec km-background supervisorctl status || log_warning "无法获取supervisorctl状态"
    
else
    log_warning "supervisorctl不可用"
fi

# 4. 检查Celery worker进程
echo ""
log_info "步骤4: 检查各个Celery worker进程"
echo "----------------------------------------"

# 定义期望的worker列表
workers=(
    "celery_worker_primary"
    "celery_worker_light" 
    "celery_worker_heavy"
    "celery_worker_docprocessing"
    "celery_worker_user_files_indexing"
    "celery_worker_docfetching"
    "celery_worker_monitoring"
    "celery_worker_kg_processing"
    "celery_beat"
)

log_info "检查期望的worker进程..."
for worker in "${workers[@]}"; do
    if docker exec km-background ps aux | grep -q "$worker"; then
        echo "✅ $worker: 运行中"
    else
        echo "❌ $worker: 未运行"
    fi
done

# 5. 检查日志文件
echo ""
log_info "步骤5: 检查Celery日志文件"
echo "----------------------------------------"

log_info "检查/var/log目录..."
docker exec km-background ls -la /var/log/ | grep -E "(celery|supervisor)" || log_warning "未找到相关日志文件"

log_info "检查supervisord日志..."
if docker exec km-background test -f /var/log/supervisord.log; then
    log_success "supervisord日志存在"
    echo "最近的supervisord日志:"
    docker exec km-background tail -10 /var/log/supervisord.log
else
    log_warning "supervisord日志不存在"
fi

# 6. 检查容器启动命令
echo ""
log_info "步骤6: 检查容器启动命令"
echo "----------------------------------------"

log_info "检查容器启动命令..."
docker inspect km-background --format='{{.Config.Cmd}}' || log_warning "无法获取启动命令"

log_info "检查容器入口点..."
docker inspect km-background --format='{{.Config.Entrypoint}}' || log_warning "无法获取入口点"

# 7. 检查Docker Compose配置
echo ""
log_info "步骤7: 检查Docker Compose中的background服务配置"
echo "----------------------------------------"

log_info "查看background服务配置..."
if [ -f "docker-compose.main.yml" ]; then
    grep -A 20 "background:" docker-compose.main.yml || log_warning "未找到background服务配置"
else
    log_warning "docker-compose.main.yml文件不存在"
fi

# 8. 尝试手动启动supervisord
echo ""
log_info "步骤8: 诊断supervisord启动问题"
echo "----------------------------------------"

log_info "检查supervisord是否安装..."
if docker exec km-background which supervisord >/dev/null 2>&1; then
    log_success "supervisord已安装"
    
    log_info "检查supervisord版本..."
    docker exec km-background supervisord --version
    
    log_info "测试supervisord配置..."
    docker exec km-background supervisord -c /app/supervisord.conf -n &
    sleep 5
    
    log_info "检查测试启动结果..."
    if docker exec km-background ps aux | grep -q supervisord; then
        log_success "supervisord可以正常启动"
        # 停止测试进程
        docker exec km-background pkill supervisord || true
    else
        log_error "supervisord启动失败"
    fi
else
    log_error "supervisord未安装"
fi

# 9. 生成诊断报告
echo ""
log_info "步骤9: 生成诊断报告"
echo "----------------------------------------"

echo ""
echo "📋 Supervisord诊断报告:"
echo "1. supervisord进程: $(docker exec km-background ps aux | grep -q supervisord && echo '✅ 运行中' || echo '❌ 未运行')"
echo "2. supervisorctl可用: $(docker exec km-background which supervisorctl >/dev/null 2>&1 && echo '✅ 可用' || echo '❌ 不可用')"
echo "3. 配置文件: $(docker exec km-background test -f /app/supervisord.conf && echo '✅ 存在' || echo '❌ 不存在')"
echo "4. 日志目录: $(docker exec km-background test -d /var/log && echo '✅ 存在' || echo '❌ 不存在')"

echo ""
echo "🔧 可能的问题和解决方案:"
echo "1. 如果supervisord未运行，需要在容器启动时启动它"
echo "2. 如果配置文件路径不对，需要调整Docker配置"
echo "3. 如果worker进程未启动，检查supervisord配置"
echo "4. 如果日志文件不存在，检查权限和目录"

echo ""
echo "📱 修复建议:"
echo "# 手动启动supervisord"
echo "docker exec km-background supervisord -c /app/supervisord.conf"
echo ""
echo "# 查看supervisord状态"
echo "docker exec km-background supervisorctl status"
echo ""
echo "# 重启所有worker"
echo "docker exec km-background supervisorctl restart all"
