#!/bin/bash

set -e

echo "🔧 使用Onyx配置修复Vespa应用包"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 检查必要文件
log_info "检查Onyx Vespa配置文件..."
VESPA_CONFIG_DIR="backend/onyx/document_index/vespa/app_config"

if [ ! -f "$VESPA_CONFIG_DIR/services.xml.jinja" ]; then
    log_error "找不到services.xml.jinja文件"
    exit 1
fi

if [ ! -f "$VESPA_CONFIG_DIR/schemas/danswer_chunk.sd.jinja" ]; then
    log_error "找不到danswer_chunk.sd.jinja文件"
    exit 1
fi

log_success "Onyx配置文件检查通过"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    log_error "需要Python3环境"
    exit 1
fi

# 安装必要的Python包
log_info "安装必要的Python包..."
pip3 install jinja2 requests 2>/dev/null || {
    log_warning "无法安装Python包，尝试使用系统包管理器..."
    # 尝试使用系统包管理器
    if command -v dnf &> /dev/null; then
        sudo dnf install -y python3-jinja2 python3-requests
    elif command -v apt &> /dev/null; then
        sudo apt update && sudo apt install -y python3-jinja2 python3-requests
    else
        log_error "无法安装必要的Python包"
        exit 1
    fi
}

# 创建简化的部署脚本
log_info "创建应用包部署脚本..."
cat > /tmp/deploy_vespa_simple.py << 'EOF'
#!/usr/bin/env python3
import os
import zipfile
import requests
import time
from datetime import datetime, timedelta

def create_hosts_xml():
    return """<?xml version="1.0" encoding="utf-8" ?>
<hosts>
  <host name="localhost">
    <alias>danswer-node</alias>
  </host>
</hosts>"""

def create_simple_services_xml():
    return """<?xml version="1.0" encoding="utf-8" ?>
<services version="1.0">
    <container id="default" version="1.0">
        <document-api/>
        <search/>
        <http>
            <server id="default" port="8080"/>
        </http>
        <nodes>
            <node hostalias="danswer-node" />
        </nodes>
    </container>
    <content id="danswer_index" version="1.0">
        <redundancy>1</redundancy>
        <documents>
            <document type="danswer_chunk" mode="index" />
        </documents>
        <nodes>
            <node hostalias="danswer-node" distribution-key="0" />
        </nodes>
        <tuning>
            <resource-limits>
                <disk>0.85</disk>
            </resource-limits>
        </tuning>
        <engine>    
            <proton>
                <tuning>
                    <searchnode>
                        <requestthreads>
                            <persearch>4</persearch>
                        </requestthreads>
                    </searchnode>
                </tuning>
            </proton>
        </engine>
    </content>
</services>"""

def create_simple_schema():
    return """schema danswer_chunk {
    document danswer_chunk {
        field document_id type string {
            indexing: summary | attribute
            rank: filter
            attribute: fast-search
        }
        field chunk_id type int {
            indexing: summary | attribute
        }
        field semantic_identifier type string {
            indexing: summary | attribute
        }
        field title type string {
            indexing: summary | index | attribute
            index: enable-bm25
        }
        field content type string {
            indexing: summary | index
            index: enable-bm25
        }
        field content_summary type string {
            indexing: summary | index
            summary: dynamic
        }
        field title_embedding type tensor<float>(x[1024]) {
            indexing: attribute | index
            attribute {
                distance-metric: angular
            }
        }
        field embeddings type tensor<float>(t{},x[1024]) {
            indexing: attribute | index
            attribute {
                distance-metric: angular
            }
        }
        field source_type type string {
            indexing: summary | attribute
            rank: filter
            attribute: fast-search
        }
        field boost type float {
            indexing: summary | attribute
        }
        field hidden type bool {
            indexing: summary | attribute
            rank: filter
        }
        field metadata type string {
            indexing: summary | attribute
        }
        field doc_updated_at type int {
            indexing: summary | attribute
        }
        field access_control_list type weightedset<string> {
            indexing: summary | attribute
            rank: filter
            attribute: fast-search
        }
        field document_sets type weightedset<string> {
            indexing: summary | attribute
            rank: filter
            attribute: fast-search
        }
    }
    
    fieldset default {
        fields: content, title
    }
    
    rank-profile default {
        first-phase {
            expression: nativeRank(title, content)
        }
    }
}"""

def create_validation_overrides():
    until_date = (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")
    return f"""<validation-overrides>
    <allow
        until="{until_date}"
        comment="We need to be able to create/delete indices for swapping models">schema-removal</allow>
    <allow
        until="{until_date}"
        comment="We need to be able to update the schema for updates to the Onyx schema">indexing-change</allow>
    <allow
        until="{until_date}"
        comment="Prevents old alt indices from interfering with changes">field-type-change</allow>
</validation-overrides>"""

def main():
    print("📦 创建简化的Vespa应用包...")
    
    # 创建应用包目录
    app_dir = "/tmp/vespa-onyx-app"
    os.makedirs(app_dir, exist_ok=True)
    os.makedirs(os.path.join(app_dir, "schemas"), exist_ok=True)
    
    # 创建文件
    with open(os.path.join(app_dir, "hosts.xml"), 'w') as f:
        f.write(create_hosts_xml())
    
    with open(os.path.join(app_dir, "services.xml"), 'w') as f:
        f.write(create_simple_services_xml())
    
    with open(os.path.join(app_dir, "schemas", "danswer_chunk.sd"), 'w') as f:
        f.write(create_simple_schema())
    
    with open(os.path.join(app_dir, "validation-overrides.xml"), 'w') as f:
        f.write(create_validation_overrides())
    
    # 打包
    zip_path = "/tmp/vespa-onyx-app.zip"
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(app_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, app_dir)
                zipf.write(file_path, arcname)
    
    print(f"✅ 应用包已创建: {zip_path}")
    
    # 部署
    print("🚀 部署应用包...")
    deploy_url = "http://localhost:19071/application/v2/tenant/default/prepareandactivate"
    
    with open(zip_path, 'rb') as f:
        response = requests.post(
            deploy_url,
            data=f.read(),
            headers={'Content-Type': 'application/zip'},
            timeout=300
        )
    
    if response.status_code == 200:
        print("✅ 应用包部署成功")
        
        # 等待就绪
        print("⏳ 等待应用就绪...")
        for i in range(30):  # 等待5分钟
            try:
                resp = requests.get("http://localhost:8081/ApplicationStatus", timeout=10)
                if resp.status_code == 200:
                    print("✅ 应用已就绪")
                    return 0
            except:
                pass
            time.sleep(10)
            print(f"等待中... {(i+1)*10}/300秒")
        
        print("❌ 应用就绪超时")
        return 1
    else:
        print(f"❌ 部署失败: {response.status_code} - {response.text}")
        return 1

if __name__ == "__main__":
    exit(main())
EOF

# 执行部署
log_info "执行Vespa应用包部署..."
python3 /tmp/deploy_vespa_simple.py

if [ $? -eq 0 ]; then
    log_success "Vespa应用包部署成功！"
    
    # 重启相关服务
    log_info "重启相关服务..."
    docker compose -f docker-compose.main.yml restart background
    
    sleep 30
    
    # 验证修复结果
    log_info "验证修复结果..."
    echo "Vespa容器状态:"
    docker ps | grep km-vespa
    
    echo ""
    echo "Vespa健康检查:"
    curl -s http://localhost:8081/ApplicationStatus | head -3 || echo "健康检查失败"
    
    echo ""
    echo "Background服务日志检查:"
    docker logs km-background --tail=10 | grep -i vespa || log_success "未发现Vespa相关错误"
    
    echo ""
    log_success "修复完成！"
    echo "📋 访问地址："
    echo "- Vespa: http://**********:8081"
    echo "- API: http://**********:8080"
    echo "- Web: http://**********:3000"
    
else
    log_error "Vespa应用包部署失败"
    exit 1
fi
