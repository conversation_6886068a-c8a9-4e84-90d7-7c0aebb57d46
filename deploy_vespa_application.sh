#!/bin/bash

set -e

echo "📦 Vespa应用包部署脚本"
echo "========================"

# 创建Vespa应用包目录
mkdir -p vespa-app/{schemas,services.xml}

# 创建基本的services.xml配置
cat > vespa-app/services.xml << 'EOF'
<?xml version="1.0" encoding="utf-8" ?>
<services version="1.0">
  <container version="1.0" id="default">
    <search />
    <document-api />
    <http>
      <server id="default" port="8080" />
    </http>
  </container>

  <content version="1.0" id="content" cluster="content">
    <redundancy>1</redundancy>
    <documents>
      <document type="document" mode="index" />
    </documents>
    <nodes>
      <node hostalias="node1" distribution-key="0" />
    </nodes>
  </content>
</services>
EOF

# 创建基本的文档schema
cat > vespa-app/schemas/document.sd << 'EOF'
schema document {
    document document {
        field id type string {
            indexing: summary | attribute
        }
        field title type string {
            indexing: index | summary
        }
        field content type string {
            indexing: index | summary
        }
        field embedding type tensor<float>(x[1024]) {
            indexing: attribute | summary
        }
    }
    
    fieldset default {
        fields: title, content
    }
    
    rank-profile default {
        first-phase {
            expression: nativeRank(title, content)
        }
    }
}
EOF

# 创建hosts.xml
cat > vespa-app/hosts.xml << 'EOF'
<?xml version="1.0" encoding="utf-8" ?>
<hosts>
  <host name="localhost">
    <alias>node1</alias>
  </host>
</hosts>
EOF

echo "✅ Vespa应用包创建完成"

# 等待Vespa服务就绪
echo "⏳ 等待Vespa服务就绪..."
timeout=300
elapsed=0
while ! curl -f http://localhost:8081/ApplicationStatus &>/dev/null; do
    sleep 5
    elapsed=$((elapsed + 5))
    echo "等待中... ${elapsed}/${timeout}秒"
    if [[ $elapsed -ge $timeout ]]; then
        echo "❌ Vespa服务未就绪，无法部署应用包"
        exit 1
    fi
done

echo "✅ Vespa服务已就绪"

# 部署应用包
echo "🚀 部署Vespa应用包..."
if command -v vespa &> /dev/null; then
    # 使用Vespa CLI部署
    vespa config set target local
    vespa deploy vespa-app --wait 300
else
    # 使用curl直接部署
    echo "使用HTTP API部署应用包..."
    cd vespa-app
    zip -r ../application.zip .
    cd ..
    
    curl -X POST \
        -H "Content-Type: application/zip" \
        --data-binary @application.zip \
        http://localhost:19071/application/v2/tenant/default/prepareandactivate
fi

echo "✅ Vespa应用包部署完成"

# 验证部署
echo "🔍 验证应用包部署状态..."
curl -s http://localhost:8081/ApplicationStatus

echo ""
echo "📋 部署完成！"
echo "现在可以重新启动其他服务了。"
