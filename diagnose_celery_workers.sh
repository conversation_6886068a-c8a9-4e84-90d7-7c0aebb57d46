#!/bin/bash

set -e

echo "🔍 Celery Worker诊断工具"
echo "========================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 1. 检查容器状态
echo ""
log_info "步骤1: 检查km-background容器状态"
echo "----------------------------------------"

if docker ps | grep -q km-background; then
    log_success "km-background容器正在运行"
    
    # 检查容器资源使用
    log_info "容器资源使用情况:"
    docker stats km-background --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"
else
    log_error "km-background容器未运行"
    exit 1
fi

# 2. 检查Celery进程
echo ""
log_info "步骤2: 检查Celery进程状态"
echo "----------------------------------------"

log_info "查找Celery相关进程..."
docker exec km-background ps aux | grep -E "(celery|worker)" | grep -v grep || log_warning "未找到Celery进程"

echo ""
log_info "检查Python进程..."
docker exec km-background ps aux | grep python | grep -v grep || log_warning "未找到Python进程"

# 3. 检查Redis连接
echo ""
log_info "步骤3: 检查Redis连接"
echo "----------------------------------------"

log_info "测试Redis连接..."
docker run --rm redis:6 redis-cli -h 10.0.83.36 -p 6379 ping || log_error "Redis连接测试失败"

# 4. 检查Celery配置
echo ""
log_info "步骤4: 检查Celery配置"
echo "----------------------------------------"

log_info "检查Celery应用配置..."
docker exec km-background python3 -c "
import sys
sys.path.append('/app')

try:
    from onyx.background.celery.versioned_apps.primary import celery_app
    
    print(f'Celery应用名称: {celery_app.main}')
    print(f'Broker URL: {celery_app.conf.broker_url}')
    print(f'Result Backend: {celery_app.conf.result_backend}')
    
    # 检查注册的任务
    registered_tasks = list(celery_app.tasks.keys())
    print(f'注册的任务数量: {len(registered_tasks)}')
    
    # 显示一些关键任务
    indexing_tasks = [task for task in registered_tasks if 'indexing' in task.lower() or 'docfetching' in task.lower()]
    print(f'索引相关任务: {len(indexing_tasks)}')
    for task in indexing_tasks[:5]:
        print(f'  - {task}')
        
except Exception as e:
    print(f'Celery配置检查失败: {e}')
    import traceback
    traceback.print_exc()
" || log_error "Celery配置检查失败"

# 5. 尝试启动Worker
echo ""
log_info "步骤5: 检查Worker启动状态"
echo "----------------------------------------"

log_info "查看容器启动日志（最近50行）..."
docker logs km-background --tail=50 | grep -E "(celery|worker|error|exception)" || log_warning "未找到相关日志"

echo ""
log_info "检查是否有Worker启动脚本..."
docker exec km-background find /app -name "*celery*" -o -name "*worker*" | head -10

# 6. 手动测试Worker连接
echo ""
log_info "步骤6: 手动测试Worker连接"
echo "----------------------------------------"

log_info "尝试手动连接Celery..."
docker exec km-background timeout 10 python3 -c "
import sys
sys.path.append('/app')

try:
    from onyx.background.celery.versioned_apps.primary import celery_app
    
    print('尝试连接Celery broker...')
    
    # 尝试获取worker状态
    inspect = celery_app.control.inspect(timeout=5)
    
    print('检查活跃worker...')
    active = inspect.active()
    if active:
        print(f'找到活跃worker: {list(active.keys())}')
    else:
        print('没有找到活跃worker')
    
    print('检查注册的worker...')
    registered = inspect.registered()
    if registered:
        print(f'注册的worker: {list(registered.keys())}')
    else:
        print('没有注册的worker')
        
except Exception as e:
    print(f'手动连接失败: {e}')
    import traceback
    traceback.print_exc()
" || echo "手动连接测试超时或失败"

# 7. 检查环境变量
echo ""
log_info "步骤7: 检查关键环境变量"
echo "----------------------------------------"

log_info "Celery相关环境变量:"
docker exec km-background env | grep -E "(CELERY|REDIS|WORKER)" || log_warning "未找到相关环境变量"

# 8. 生成诊断报告
echo ""
log_info "步骤8: 生成诊断报告"
echo "----------------------------------------"

echo ""
echo "📋 Celery Worker诊断报告:"
echo "1. 容器状态: $(docker ps | grep -q km-background && echo '✅ 运行中' || echo '❌ 未运行')"
echo "2. Celery进程: $(docker exec km-background ps aux | grep -q celery && echo '✅ 存在' || echo '❌ 不存在')"
echo "3. Redis连接: $(docker exec km-background python3 -c 'import sys; sys.path.append(\"/app\"); from onyx.configs.app_configs import REDIS_HOST, REDIS_PORT, REDIS_DB_NUMBER_CELERY; import redis; redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB_NUMBER_CELERY).ping()' 2>/dev/null && echo '✅ 正常' || echo '❌ 异常')"

echo ""
echo "🔧 可能的解决方案:"
echo "1. 如果没有Celery进程，需要启动worker"
echo "2. 如果Redis连接异常，检查网络和Redis服务"
echo "3. 如果配置有问题，检查环境变量"
echo "4. 重启background容器"

echo ""
echo "📱 修复命令:"
echo "# 重启background容器"
echo "docker compose -f docker-compose.main.yml restart background"
echo ""
echo "# 手动启动worker（如果需要）"
echo "docker exec -d km-background celery -A onyx.background.celery.versioned_apps.primary worker --loglevel=info"
echo ""
echo "# 查看实时日志"
echo "docker logs km-background -f"
