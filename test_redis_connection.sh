#!/bin/bash

set -e

echo "🔍 Redis连接测试工具"
echo "===================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

REDIS_HOST="**********"
REDIS_PORT="6379"

echo ""
log_info "测试Redis连接到 ${REDIS_HOST}:${REDIS_PORT}"
echo "----------------------------------------"

# 1. 测试网络连通性
log_info "1. 测试网络连通性..."
if ping -c 3 ${REDIS_HOST} >/dev/null 2>&1; then
    log_success "网络连通性正常"
else
    log_error "无法ping通Redis服务器"
    exit 1
fi

# 2. 测试端口连通性
log_info "2. 测试端口连通性..."
if nc -z ${REDIS_HOST} ${REDIS_PORT} 2>/dev/null; then
    log_success "Redis端口${REDIS_PORT}可访问"
else
    log_error "Redis端口${REDIS_PORT}不可访问"
    exit 1
fi

# 3. 测试Redis服务响应
log_info "3. 测试Redis服务响应..."
if redis-cli -h ${REDIS_HOST} -p ${REDIS_PORT} ping 2>/dev/null | grep -q "PONG"; then
    log_success "Redis服务响应正常"
else
    log_error "Redis服务无响应"
    exit 1
fi

# 4. 测试从容器内连接
log_info "4. 测试从km-background容器内连接..."
if docker exec km-background python3 -c "
import redis
try:
    r = redis.Redis(host='${REDIS_HOST}', port=${REDIS_PORT}, db=0, socket_timeout=5)
    result = r.ping()
    print(f'Redis连接成功: {result}')
    
    # 测试基本操作
    r.set('test_key', 'test_value')
    value = r.get('test_key')
    print(f'读写测试: {value.decode() if value else None}')
    r.delete('test_key')
    
except Exception as e:
    print(f'Redis连接失败: {e}')
    import traceback
    traceback.print_exc()
" 2>/dev/null; then
    log_success "容器内Redis连接正常"
else
    log_error "容器内Redis连接失败"
    
    # 详细诊断
    log_info "进行详细诊断..."
    docker exec km-background python3 -c "
import redis
import socket
try:
    # 测试DNS解析
    ip = socket.gethostbyname('${REDIS_HOST}')
    print(f'DNS解析: ${REDIS_HOST} -> {ip}')
    
    # 测试socket连接
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(5)
    result = sock.connect_ex(('${REDIS_HOST}', ${REDIS_PORT}))
    sock.close()
    print(f'Socket连接测试: {\"成功\" if result == 0 else \"失败\"}')
    
    # 测试Redis连接详情
    r = redis.Redis(host='${REDIS_HOST}', port=${REDIS_PORT}, db=0, socket_timeout=5)
    info = r.info()
    print(f'Redis版本: {info.get(\"redis_version\", \"未知\")}')
    
except Exception as e:
    print(f'详细诊断失败: {e}')
    import traceback
    traceback.print_exc()
"
fi

# 5. 检查Redis配置
log_info "5. 检查Redis服务配置..."
redis-cli -h ${REDIS_HOST} -p ${REDIS_PORT} info server | head -10

# 6. 检查容器环境变量
log_info "6. 检查容器Redis环境变量..."
docker exec km-background env | grep -i redis

# 7. 测试Celery连接
log_info "7. 测试Celery Redis连接..."
docker exec km-background python3 -c "
import sys
sys.path.append('/app')

try:
    from onyx.configs.app_configs import REDIS_HOST, REDIS_PORT, REDIS_DB_NUMBER_CELERY
    import redis
    
    print(f'配置的Redis地址: {REDIS_HOST}:{REDIS_PORT}')
    print(f'Celery数据库: {REDIS_DB_NUMBER_CELERY}')
    
    # 测试Celery Redis连接
    r = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB_NUMBER_CELERY)
    result = r.ping()
    print(f'Celery Redis连接: {\"成功\" if result else \"失败\"}')
    
    # 检查Celery队列
    keys = r.keys('celery*')
    print(f'Celery相关键数量: {len(keys)}')
    
except Exception as e:
    print(f'Celery Redis测试失败: {e}')
    import traceback
    traceback.print_exc()
"

echo ""
log_success "Redis连接测试完成！"
echo ""
echo "📋 测试总结:"
echo "1. 网络连通性: ✅"
echo "2. 端口可访问性: ✅" 
echo "3. Redis服务响应: ✅"
echo "4. 容器内连接: $(docker exec km-background python3 -c 'import redis; redis.Redis(host=\"${REDIS_HOST}\").ping()' 2>/dev/null && echo '✅' || echo '❌')"
echo "5. Celery连接: $(docker exec km-background python3 -c 'import sys; sys.path.append(\"/app\"); from onyx.configs.app_configs import REDIS_HOST, REDIS_PORT, REDIS_DB_NUMBER_CELERY; import redis; redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB_NUMBER_CELERY).ping()' 2>/dev/null && echo '✅' || echo '❌')"

echo ""
echo "🔧 如果连接失败，请检查："
echo "1. **********服务器上的Redis服务是否正常运行"
echo "2. 防火墙是否开放6379端口"
echo "3. Redis配置是否允许远程连接"
echo "4. 网络路由是否正常"
