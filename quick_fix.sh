#!/bin/bash
# Onyx快速修复脚本 - 针对索引问题的快速解决方案

echo "=== Onyx快速修复脚本 ==="
echo "服务器: 10.0.83.30"
echo "时间: $(date)"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() { echo -e "${GREEN}✓${NC} $1"; }
log_warn() { echo -e "${YELLOW}⚠${NC} $1"; }
log_error() { echo -e "${RED}✗${NC} $1"; }

# 检查Docker Compose文件
if [ -f "docker-compose.main.yml" ]; then
    COMPOSE_FILE="docker-compose.main.yml"
else
    log_error "未找到docker-compose.main.yml文件"
    exit 1
fi

echo "使用配置文件: $COMPOSE_FILE"
echo ""

# 1. 检查容器状态
echo "1. 检查容器状态..."
docker ps --format "table {{.Names}}\t{{.Status}}"
echo ""

# 2. 重启Vespa服务 (主要问题)
echo "2. 重启Vespa服务..."
VESPA_CONTAINERS=$(docker ps -a --filter "name=vespa" --format "{{.Names}}" || true)
if [ -n "$VESPA_CONTAINERS" ]; then
    for container in $VESPA_CONTAINERS; do
        echo "重启Vespa容器: $container"
        docker restart $container
    done
    log_info "Vespa服务已重启"
    
    # 等待Vespa启动
    echo "等待Vespa启动 (30秒)..."
    sleep 30
    
    # 检查Vespa状态
    for container in $VESPA_CONTAINERS; do
        echo "检查 $container 健康状态..."
        if docker exec $container curl -s http://localhost:8080/ApplicationStatus > /dev/null 2>&1; then
            log_info "$container 响应正常"
        else
            log_warn "$container 可能还在启动中"
        fi
    done
else
    log_error "未找到Vespa容器"
fi
echo ""

# 3. 清理Redis锁
echo "3. 清理Redis锁..."
REDIS_CONTAINERS=$(docker ps --filter "name=redis" --format "{{.Names}}" || true)
if [ -n "$REDIS_CONTAINERS" ]; then
    for container in $REDIS_CONTAINERS; do
        echo "检查 $container 中的锁..."
        lock_count=$(docker exec $container redis-cli --scan --pattern "*lock*" | wc -l)
        if [ "$lock_count" -gt 0 ]; then
            log_warn "发现 $lock_count 个锁，正在清理..."
            docker exec $container redis-cli --scan --pattern "*lock*" | xargs -r docker exec $container redis-cli del
            log_info "Redis锁已清理"
        else
            log_info "没有发现锁"
        fi
    done
else
    log_error "未找到Redis容器"
fi
echo ""

# 4. 重启Celery Worker
echo "4. 重启Celery Worker..."
WORKER_CONTAINERS=$(docker ps --filter "name=worker" --format "{{.Names}}" || true)
if [ -n "$WORKER_CONTAINERS" ]; then
    for container in $WORKER_CONTAINERS; do
        echo "重启Worker容器: $container"
        docker restart $container
    done
    log_info "Celery Worker已重启"
else
    # 尝试查找包含celery的容器
    CELERY_CONTAINERS=$(docker ps --filter "name=celery" --format "{{.Names}}" || true)
    if [ -n "$CELERY_CONTAINERS" ]; then
        for container in $CELERY_CONTAINERS; do
            echo "重启Celery容器: $container"
            docker restart $container
        done
        log_info "Celery容器已重启"
    else
        log_warn "未找到Worker或Celery容器"
    fi
fi
echo ""

# 5. 检查服务状态
echo "5. 检查修复后的服务状态..."
echo "容器状态:"
docker ps --format "table {{.Names}}\t{{.Status}}"
echo ""

# 6. 查看最近的日志
echo "6. 查看最近的日志 (最后20行)..."
docker compose -f $COMPOSE_FILE logs --tail=20 --timestamps
echo ""

echo "=== 快速修复完成 ==="
echo ""
echo "建议接下来:"
echo "1. 等待5-10分钟让服务完全启动"
echo "2. 检查是否有新的索引任务开始执行"
echo "3. 如果问题仍然存在，运行完整诊断: ./server_diagnose.sh"
echo "4. 查看实时日志: docker compose -f $COMPOSE_FILE logs -f"
echo ""
