[supervisord]
loglevel=info                ; log level; default info; others: debug,warn,trace
logfile=/tmp/issue-1231b.log ; main log file; default $CWD/supervisord.log
pidfile=/tmp/issue-1231b.pid ; supervisord pidfile; default supervisord.pid
nodaemon=true                ; start in foreground if true; default false

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[unix_http_server]
file=/tmp/issue-1231b.sock   ; the path to the socket file

[supervisorctl]
serverurl=unix:///tmp/issue-1231b.sock  ; use a unix:// URL  for a unix socket

[program:hello]
command=python %(here)s/test_1231.py
