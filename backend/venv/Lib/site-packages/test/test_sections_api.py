# coding: utf-8

"""
    Asana

    This is the interface for interacting with the [Asana Platform](https://developers.asana.com). Our API reference is generated from our [OpenAPI spec] (https://raw.githubusercontent.com/Asana/openapi/master/defs/asana_oas.yaml).  # noqa: E501

    OpenAPI spec version: 1.0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""

from __future__ import absolute_import

import unittest

import asana
from asana.api.sections_api import SectionsApi  # noqa: E501
from asana.rest import ApiException


class TestSectionsApi(unittest.TestCase):
    """SectionsApi unit test stubs"""

    def setUp(self):
        self.api = SectionsApi()  # noqa: E501

    def tearDown(self):
        pass

    def test_add_task_for_section(self):
        """Test case for add_task_for_section

        Add task to section  # noqa: E501
        """
        pass

    def test_create_section_for_project(self):
        """Test case for create_section_for_project

        Create a section in a project  # noqa: E501
        """
        pass

    def test_delete_section(self):
        """Test case for delete_section

        Delete a section  # noqa: E501
        """
        pass

    def test_get_section(self):
        """Test case for get_section

        Get a section  # noqa: E501
        """
        pass

    def test_get_sections_for_project(self):
        """Test case for get_sections_for_project

        Get sections in a project  # noqa: E501
        """
        pass

    def test_insert_section_for_project(self):
        """Test case for insert_section_for_project

        Move or Insert sections  # noqa: E501
        """
        pass

    def test_update_section(self):
        """Test case for update_section

        Update a section  # noqa: E501
        """
        pass


if __name__ == '__main__':
    unittest.main()
