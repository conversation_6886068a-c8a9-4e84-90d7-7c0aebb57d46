#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API to_padded_tensor {
  using schema = at::Tensor (const at::Tensor &, double, at::OptionalSymIntArrayRef);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::to_padded_tensor";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "to_padded_tensor(Tensor self, float padding, SymInt[]? output_size=None) -> Tensor";
  static at::Tensor call(const at::Tensor & self, double padding, at::OptionalSymIntArrayRef output_size);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, double padding, at::OptionalSymIntArrayRef output_size);
};

struct TORCH_API to_padded_tensor_out {
  using schema = at::Tensor & (const at::Tensor &, double, at::OptionalSymIntArrayRef, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::to_padded_tensor";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "to_padded_tensor.out(Tensor self, float padding, SymInt[]? output_size=None, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, double padding, at::OptionalSymIntArrayRef output_size, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, double padding, at::OptionalSymIntArrayRef output_size, at::Tensor & out);
};

}} // namespace at::_ops
