Metadata-Version: 2.1
Name: pytest-base-url
Version: 2.1.0
Summary: pytest plugin for URL based testing
Project-URL: Homepage, https://github.com/pytest-dev/pytest-base-url
Project-URL: Tracker, https://github.com/pytest-dev/pytest-base-url/issues
Project-URL: Source, https://github.com/pytest-dev/pytest-base-url
Author-email: <PERSON> <<EMAIL>>, <PERSON> <jimbran<PERSON><PERSON>@fastmail.com>
License-Expression: MPL-2.0
License-File: LICENSE
Keywords: automation,base,mozilla,pytest,url
Classifier: Development Status :: 5 - Production/Stable
Classifier: Framework :: Pytest
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Mozilla Public License 2.0 (MPL 2.0)
Classifier: Natural Language :: English
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Software Development :: Quality Assurance
Classifier: Topic :: Software Development :: Testing
Classifier: Topic :: Utilities
Requires-Python: >=3.8
Requires-Dist: pytest>=7.0.0
Requires-Dist: requests>=2.9
Provides-Extra: test
Requires-Dist: black>=22.1.0; extra == 'test'
Requires-Dist: flake8>=4.0.1; extra == 'test'
Requires-Dist: pre-commit>=2.17.0; extra == 'test'
Requires-Dist: pytest-localserver>=0.7.1; extra == 'test'
Requires-Dist: tox>=3.24.5; extra == 'test'
Description-Content-Type: text/x-rst

pytest-base-url
===============

pytest-base-url is a simple plugin for pytest_ that provides an optional base
URL via the command line or configuration file.

.. image:: https://img.shields.io/badge/license-MPL%202.0-blue.svg
   :target: https://github.com/pytest-dev/pytest-base-url/blob/master/LICENSE
   :alt: License
.. image:: https://img.shields.io/pypi/v/pytest-base-url.svg
   :target: https://pypi.python.org/pypi/pytest-base-url/
   :alt: PyPI
.. image:: https://img.shields.io/travis/pytest-dev/pytest-base-url.svg
   :target: https://travis-ci.org/pytest-dev/pytest-base-url/
   :alt: Travis
.. image:: https://img.shields.io/github/issues-raw/pytest-dev/pytest-base-url.svg
   :target: https://github.com/pytest-dev/pytest-base-url/issues
   :alt: Issues
.. image:: https://img.shields.io/requires/github/pytest-dev/pytest-base-url.svg
   :target: https://requires.io/github/pytest-dev/pytest-base-url/requirements/?branch=master
   :alt: Requirements

Requirements
------------

You will need the following prerequisites in order to use pytest-base-url:

- Python 3.8+ or PyPy3

Installation
------------

To install pytest-base-url:

.. code-block:: bash

  $ pip install pytest-base-url

Contributing
------------

We welcome contributions.

To learn more, see `Development <https://github.com/pytest-dev/pytest-base-url/blob/master/development.rst>`_

Specifying a Base URL
---------------------

Rather than repeating or abstracting a base URL in your tests, pytest-base-url
provides a ``base_url`` fixture that returns the specified base URL.

.. code-block:: python

  import urllib2

  def test_example(base_url):
      assert 200 == urllib2.urlopen(base_url).getcode()

Using the Command Line
^^^^^^^^^^^^^^^^^^^^^^

You can specify the base URL on the command line:

.. code-block:: bash

  $ pytest --base-url http://www.example.com

Using a Configuration File
^^^^^^^^^^^^^^^^^^^^^^^^^^

You can specify the base URL using a `configuration file`_:

.. code-block:: ini

  [pytest]
  base_url = http://www.example.com

Using an Environment Variable
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

You can specify the base URL by setting the ``PYTEST_BASE_URL`` environment variable.

Using a Fixture
^^^^^^^^^^^^^^^

If your test harness takes care of launching an instance of your application
under test, you may not have a predictable base URL to provide on the command
line. Fortunately, it's easy to override the ``base_url`` fixture and return
the correct URL to your test.

In the following example a ``live_server`` fixture is used to start the
application and ``live_server.url`` returns the base URL of the site.

.. code-block:: python

  import urllib2
  import pytest

  @pytest.fixture
  def base_url(live_server):
      return live_server.url

  def test_search(base_url):
      assert 200 == urllib2.urlopen('{0}/search'.format(base_url)).getcode()

Available Live Servers
----------------------

It's relatively simple to create your own ``live_server`` fixture, however you
may be able to take advantage of one of the following:

* Django applications can use pytest-django_'s  ``live_server`` fixture.
* Flask applications can use pytest-flask_'s ``live_server`` fixture.

Verifying the Base URL
----------------------

If you specify a base URL for a site that's unavailable then all tests using
that base URL will likely fail. To avoid running every test in this instance,
you can enable base URL verification. This will check the base URL is
responding before proceeding with the test suite. To enable this, specify the
``--verify-base-url`` command line option or set the ``VERIFY_BASE_URL``
environment variable to ``TRUE``.

Skipping Base URLs
------------------

You can `skip tests`_ based on the value of the base URL so long as it is
provided either by the command line or in a configuration file:

.. code-block:: python

  import urllib2
  import pytest

  @pytest.mark.skipif(
      "'dev' in config.getoption('base_url')",
      reason='Search not available on dev')
  def test_search(base_url):
      assert 200 == urllib2.urlopen('{0}/search'.format(base_url)).getcode()

Unfortunately if the URL is provided by a fixture, there is no way to know this
value at test collection.

Resources
---------

- `Release Notes`_
- `Issue Tracker`_
- Code_

.. _pytest: http://www.python.org/
.. _configuration file: http://pytest.org/latest/customize.html#command-line-options-and-configuration-file-settings
.. _pytest-django: http://pytest-django.readthedocs.org/
.. _pytest-flask: http://pytest-flask.readthedocs.org/
.. _skip tests: http://pytest.org/latest/skipping.html
.. _Release Notes:  http://github.com/pytest-dev/pytest-base-url/blob/master/CHANGES.rst
.. _Issue Tracker: http://github.com/pytest-dev/pytest-base-url/issues
.. _Code: http://github.com/pytest-dev/pytest-base-url
