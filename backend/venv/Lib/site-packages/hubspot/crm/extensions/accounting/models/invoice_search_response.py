# coding: utf-8

"""
    Accounting Extension

    These APIs allow you to interact with HubSpot's Accounting Extension. It allows you to: * Specify the URLs that HubSpot will use when making webhook requests to your external accounting system. * Respond to webhook calls made to your external accounting system by HubSpot   # noqa: E501

    The version of the OpenAPI document: v3
    Generated by: https://openapi-generator.tech
"""


try:
    from inspect import getfullargspec
except ImportError:
    from inspect import getargspec as getfullargspec
import pprint
import re  # noqa: F401
import six

from hubspot.crm.extensions.accounting.configuration import Configuration


class InvoiceSearchResponse(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {"result": "str", "invoices": "list[AccountingExtensionInvoice]"}

    attribute_map = {"result": "@result", "invoices": "invoices"}

    def __init__(self, result=None, invoices=None, local_vars_configuration=None):  # noqa: E501
        """InvoiceSearchResponse - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration.get_default_copy()
        self.local_vars_configuration = local_vars_configuration

        self._result = None
        self._invoices = None
        self.discriminator = None

        if result is not None:
            self.result = result
        self.invoices = invoices

    @property
    def result(self):
        """Gets the result of this InvoiceSearchResponse.  # noqa: E501

        Designates if the response is a success ('OK') or failure ('ERR').  # noqa: E501

        :return: The result of this InvoiceSearchResponse.  # noqa: E501
        :rtype: str
        """
        return self._result

    @result.setter
    def result(self, result):
        """Sets the result of this InvoiceSearchResponse.

        Designates if the response is a success ('OK') or failure ('ERR').  # noqa: E501

        :param result: The result of this InvoiceSearchResponse.  # noqa: E501
        :type result: str
        """
        allowed_values = ["OK", "ERR"]  # noqa: E501
        if self.local_vars_configuration.client_side_validation and result not in allowed_values:  # noqa: E501
            raise ValueError("Invalid value for `result` ({0}), must be one of {1}".format(result, allowed_values))  # noqa: E501

        self._result = result

    @property
    def invoices(self):
        """Gets the invoices of this InvoiceSearchResponse.  # noqa: E501

        The list of invoices that matched the search criteria.  # noqa: E501

        :return: The invoices of this InvoiceSearchResponse.  # noqa: E501
        :rtype: list[AccountingExtensionInvoice]
        """
        return self._invoices

    @invoices.setter
    def invoices(self, invoices):
        """Sets the invoices of this InvoiceSearchResponse.

        The list of invoices that matched the search criteria.  # noqa: E501

        :param invoices: The invoices of this InvoiceSearchResponse.  # noqa: E501
        :type invoices: list[AccountingExtensionInvoice]
        """
        if self.local_vars_configuration.client_side_validation and invoices is None:  # noqa: E501
            raise ValueError("Invalid value for `invoices`, must not be `None`")  # noqa: E501

        self._invoices = invoices

    def to_dict(self, serialize=False):
        """Returns the model properties as a dict"""
        result = {}

        def convert(x):
            if hasattr(x, "to_dict"):
                args = getfullargspec(x.to_dict).args
                if len(args) == 1:
                    return x.to_dict()
                else:
                    return x.to_dict(serialize)
            else:
                return x

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            attr = self.attribute_map.get(attr, attr) if serialize else attr
            if isinstance(value, list):
                result[attr] = list(map(lambda x: convert(x), value))
            elif isinstance(value, dict):
                result[attr] = dict(map(lambda item: (item[0], convert(item[1])), value.items()))
            else:
                result[attr] = convert(value)

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InvoiceSearchResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InvoiceSearchResponse):
            return True

        return self.to_dict() != other.to_dict()
