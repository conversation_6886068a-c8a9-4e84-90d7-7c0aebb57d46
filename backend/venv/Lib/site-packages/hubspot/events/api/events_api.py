# coding: utf-8

"""
    HubSpot Events API

    API for accessing CRM object events.  # noqa: E501

    The version of the OpenAPI document: v3
    Generated by: https://openapi-generator.tech
"""


from __future__ import absolute_import

import re  # noqa: F401

# python 2 and python 3 compatibility library
import six

from hubspot.events.api_client import ApiClient
from hubspot.events.exceptions import ApiTypeError, ApiValueError  # noqa: F401


class EventsApi(object):
    """NOTE: This class is auto generated by OpenAPI Generator
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client

    def get_page(self, **kwargs):  # noqa: E501
        """Returns a collection of events matching a query.  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.get_page(async_req=True)
        >>> result = thread.get()

        :param occurred_after: The starting time as an ISO 8601 timestamp.
        :type occurred_after: datetime
        :param occurred_before: The ending time as an ISO 8601 timestamp.
        :type occurred_before: datetime
        :param object_type: The type of object being selected. Valid values are hubspot named object types (e.g. `contact`).
        :type object_type: str
        :param object_id: The id of the selected object. If not present, then the `objectProperty` parameter is required.
        :type object_id: int
        :param event_type: Limits the response to the specified event type.  For example `&eventType=e_visited_page` returns only `e_visited_page` events.  If not present all event types are returned.
        :type event_type: str
        :param after: An additional parameter that may be used to get the next `limit` set of results.
        :type after: str
        :param before:
        :type before: str
        :param limit: The maximum number of events to return, defaults to 20.
        :type limit: int
        :param sort: Selects the sort field and order. Defaults to ascending, prefix with `-` for descending order. `occurredAt` is the only field supported for sorting.
        :type sort: list[str]
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: CollectionResponseExternalUnifiedEvent
        """
        kwargs["_return_http_data_only"] = True
        return self.get_page_with_http_info(**kwargs)  # noqa: E501

    def get_page_with_http_info(self, **kwargs):  # noqa: E501
        """Returns a collection of events matching a query.  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True

        >>> thread = api.get_page_with_http_info(async_req=True)
        >>> result = thread.get()

        :param occurred_after: The starting time as an ISO 8601 timestamp.
        :type occurred_after: datetime
        :param occurred_before: The ending time as an ISO 8601 timestamp.
        :type occurred_before: datetime
        :param object_type: The type of object being selected. Valid values are hubspot named object types (e.g. `contact`).
        :type object_type: str
        :param object_id: The id of the selected object. If not present, then the `objectProperty` parameter is required.
        :type object_id: int
        :param event_type: Limits the response to the specified event type.  For example `&eventType=e_visited_page` returns only `e_visited_page` events.  If not present all event types are returned.
        :type event_type: str
        :param after: An additional parameter that may be used to get the next `limit` set of results.
        :type after: str
        :param before:
        :type before: str
        :param limit: The maximum number of events to return, defaults to 20.
        :type limit: int
        :param sort: Selects the sort field and order. Defaults to ascending, prefix with `-` for descending order. `occurredAt` is the only field supported for sorting.
        :type sort: list[str]
        :param async_req: Whether to execute the request asynchronously.
        :type async_req: bool, optional
        :param _return_http_data_only: response data without head status code
                                       and headers
        :type _return_http_data_only: bool, optional
        :param _preload_content: if False, the urllib3.HTTPResponse object will
                                 be returned without reading/decoding response
                                 data. Default is True.
        :type _preload_content: bool, optional
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the authentication
                              in the spec for a single request.
        :type _request_auth: dict, optional
        :type _content_type: string, optional: force content-type for the request
        :return: Returns the result object.
                 If the method is called asynchronously,
                 returns the request thread.
        :rtype: tuple(CollectionResponseExternalUnifiedEvent, status_code(int), headers(HTTPHeaderDict))
        """

        local_var_params = locals()

        all_params = ["occurred_after", "occurred_before", "object_type", "object_id", "event_type", "after", "before", "limit", "sort"]
        all_params.extend(["async_req", "_return_http_data_only", "_preload_content", "_request_timeout", "_request_auth", "_content_type", "_headers"])

        for key, val in six.iteritems(local_var_params["kwargs"]):
            if key not in all_params:
                raise ApiTypeError("Got an unexpected keyword argument '%s'" " to method get_page" % key)
            local_var_params[key] = val
        del local_var_params["kwargs"]

        collection_formats = {}

        path_params = {}

        query_params = []
        if local_var_params.get("occurred_after") is not None:  # noqa: E501
            query_params.append(("occurredAfter", local_var_params["occurred_after"]))  # noqa: E501
        if local_var_params.get("occurred_before") is not None:  # noqa: E501
            query_params.append(("occurredBefore", local_var_params["occurred_before"]))  # noqa: E501
        if local_var_params.get("object_type") is not None:  # noqa: E501
            query_params.append(("objectType", local_var_params["object_type"]))  # noqa: E501
        if local_var_params.get("object_id") is not None:  # noqa: E501
            query_params.append(("objectId", local_var_params["object_id"]))  # noqa: E501
        if local_var_params.get("event_type") is not None:  # noqa: E501
            query_params.append(("eventType", local_var_params["event_type"]))  # noqa: E501
        if local_var_params.get("after") is not None:  # noqa: E501
            query_params.append(("after", local_var_params["after"]))  # noqa: E501
        if local_var_params.get("before") is not None:  # noqa: E501
            query_params.append(("before", local_var_params["before"]))  # noqa: E501
        if local_var_params.get("limit") is not None:  # noqa: E501
            query_params.append(("limit", local_var_params["limit"]))  # noqa: E501
        if local_var_params.get("sort") is not None:  # noqa: E501
            query_params.append(("sort", local_var_params["sort"]))  # noqa: E501
            collection_formats["sort"] = "multi"  # noqa: E501

        header_params = dict(local_var_params.get("_headers", {}))

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json", "*/*"])  # noqa: E501

        # Authentication setting
        auth_settings = ["oauth2"]  # noqa: E501

        response_types_map = {
            200: "CollectionResponseExternalUnifiedEvent",
        }

        return self.api_client.call_api(
            "/events/v3/events",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_types_map=response_types_map,
            auth_settings=auth_settings,
            async_req=local_var_params.get("async_req"),
            _return_http_data_only=local_var_params.get("_return_http_data_only"),  # noqa: E501
            _preload_content=local_var_params.get("_preload_content", True),
            _request_timeout=local_var_params.get("_request_timeout"),
            collection_formats=collection_formats,
            _request_auth=local_var_params.get("_request_auth"),
        )
