import unittest

__author__ = "<PERSON><PERSON>"
__copyright__ = "2013-2025 <PERSON><PERSON>"
__license__ = "MPL-1.1 OR GPL-2.0-only OR LGPL-2.1-or-later"
__all__ = ("TestRegistry",)


class TestRegistry(unittest.TestCase):
    """Test registry."""

    def test_import_from_registry(self):
        """Test import from deprecated `valuta.registry` module."""
        from ..registry import Registry  # noqa
