shapely-2.0.6.dist-info/DELVEWHEEL,sha256=bTmqAkdP7MkS4K98y10HFR-7TkuaSIiiiDcEXyEHIVI,447
shapely-2.0.6.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
shapely-2.0.6.dist-info/LICENSE.txt,sha256=6M19NGBNJ1j_WJ8PNNbtecP3-xSq9eEvsJIhQhORo78,1612
shapely-2.0.6.dist-info/LICENSE_GEOS,sha256=-5Qh8jm20GqvnpAlAOZ69GNpCqfOSpHRxj-KN9-PgYw,27304
shapely-2.0.6.dist-info/LICENSE_win32,sha256=mzDNGgl4mITdVOWCjjHSv45oeQNOfPe24DIURM9t0R0,1179
shapely-2.0.6.dist-info/METADATA,sha256=Vi54E2apcA49Jz4LRKcLCJXCmxAQMjaza6SZVzb6fas,7242
shapely-2.0.6.dist-info/RECORD,,
shapely-2.0.6.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
shapely-2.0.6.dist-info/WHEEL,sha256=AFY_wrnxMeU-EzEX7KtxiLnGsh_VlRP1ZdcsKsU-pxw,101
shapely-2.0.6.dist-info/top_level.txt,sha256=fxc5UIKKldpKP3lx2dvM5R3hWeKwlmVW6-nfikr3iU0,8
shapely.libs/geos-23a442ac2dc21ec50035b705fec97a5a.dll,sha256=1m1fvY3mFTKm_MYle-9rXxUcDLAOOCgu7zj4p_CKVrY,1995776
shapely.libs/geos_c-b1ae1e27c3e522f9b7836b33163a0f55.dll,sha256=_hJh4f5ZhvEp3N6M57yi-FCCPjvOQ23yEkJffu2RfQ4,382464
shapely.libs/msvcp140-a69f03e9a25e87aaadb3f9acf048914d.dll,sha256=UnAMFErRr9GU2MY7IxsvVX1L-az5npxdn9rTgDHR8sE,567336
shapely/__init__.py,sha256=94zoVoPsfwru9NxpECsVAU4vjXVgInBaqZH_olkYDU0,1421
shapely/__pycache__/__init__.cpython-312.pyc,,
shapely/__pycache__/_enum.cpython-312.pyc,,
shapely/__pycache__/_geometry.cpython-312.pyc,,
shapely/__pycache__/_ragged_array.cpython-312.pyc,,
shapely/__pycache__/_version.cpython-312.pyc,,
shapely/__pycache__/affinity.cpython-312.pyc,,
shapely/__pycache__/constructive.cpython-312.pyc,,
shapely/__pycache__/coordinates.cpython-312.pyc,,
shapely/__pycache__/coords.cpython-312.pyc,,
shapely/__pycache__/creation.cpython-312.pyc,,
shapely/__pycache__/decorators.cpython-312.pyc,,
shapely/__pycache__/errors.cpython-312.pyc,,
shapely/__pycache__/geos.cpython-312.pyc,,
shapely/__pycache__/io.cpython-312.pyc,,
shapely/__pycache__/linear.cpython-312.pyc,,
shapely/__pycache__/measurement.cpython-312.pyc,,
shapely/__pycache__/ops.cpython-312.pyc,,
shapely/__pycache__/plotting.cpython-312.pyc,,
shapely/__pycache__/predicates.cpython-312.pyc,,
shapely/__pycache__/prepared.cpython-312.pyc,,
shapely/__pycache__/set_operations.cpython-312.pyc,,
shapely/__pycache__/speedups.cpython-312.pyc,,
shapely/__pycache__/strtree.cpython-312.pyc,,
shapely/__pycache__/testing.cpython-312.pyc,,
shapely/__pycache__/validation.cpython-312.pyc,,
shapely/__pycache__/wkb.cpython-312.pyc,,
shapely/__pycache__/wkt.cpython-312.pyc,,
shapely/_enum.py,sha256=wa7WXaDemoT7EknhUDEee29Kgz0ryDGnoxvetRHO7YE,736
shapely/_geometry.py,sha256=fTe1WRc-j0SWWMQx04dbXvNJgY177a2G4Z-Jgagoylw,25267
shapely/_geometry_helpers.cp312-win_amd64.pyd,sha256=OnXWx0eBl5lUz_q6aQNjkxAUWgjRMUxRigwvQJA7PdY,205312
shapely/_geos.cp312-win_amd64.pyd,sha256=X3acqsGUw-1N1sgA1JxtkplVLCXW9JaM9Mdf-vnIKpQ,47616
shapely/_geos.pxd,sha256=U6QtrKij8oulKsIyCdBqWiGBP6J-2fXVqjTq0sRMv10,2940
shapely/_pygeos_api.pxd,sha256=9bUgCglsVO3LgNz9Wgre40xpuAxeJdFWiDwdn2pCGws,1553
shapely/_ragged_array.py,sha256=zEzmPyel9BqZanEtE1COpktRtXJhBrKv80Bw4IJEmOw,17023
shapely/_version.py,sha256=LfP4eSO3u-hn7XtgfLBCMwW6q1o3p5PwMMuxe09zzw0,518
shapely/affinity.py,sha256=Zjetdng42-cYq2dowxQTl6GVFs28KjXbxE4LA0M4g-E,8529
shapely/algorithms/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
shapely/algorithms/__pycache__/__init__.cpython-312.pyc,,
shapely/algorithms/__pycache__/_oriented_envelope.cpython-312.pyc,,
shapely/algorithms/__pycache__/cga.cpython-312.pyc,,
shapely/algorithms/__pycache__/polylabel.cpython-312.pyc,,
shapely/algorithms/_oriented_envelope.py,sha256=ncDicOFg1OTCoY9a44Jfy2gQGWoec7VqlRouKmrxPVg,2033
shapely/algorithms/cga.py,sha256=7IRQ7I5ikO98nVWnvQ1UL30CPzdd7bNwRGJ8eL25h3s,649
shapely/algorithms/polylabel.py,sha256=dBLKu9tONovHRbOmIt-m974foTgoXSWVGhwA10HSYnI,4831
shapely/constructive.py,sha256=5FvFAib4pZ0gYMLxVzfBDVvPIuzRd0j-hFM-E_yvKq4,39226
shapely/coordinates.py,sha256=5Ifzre7JYs1XZros87NtzZuChA75ngbyAsHXV15KsBU,7418
shapely/coords.py,sha256=8i4uWYTKBWCljCgJ8BmxmDVz09RtIbUvZVRXvcSEpJs,1846
shapely/creation.py,sha256=caMOPGq9eApV17vb2prmiOt6NNYs_lbB-Wiv7ht_f6I,20837
shapely/decorators.py,sha256=wBC0i4a6hPe3QfIp23OTzeeXTT6usSm06UOkVyabszw,2637
shapely/errors.py,sha256=tm4NYtmXIk7xv_hXb8f9VjX5eIU5XfJDqzknXtj1R9g,2536
shapely/geometry/__init__.py,sha256=-0F3TG3UkT6s4_JacVuI21vfk0b2C26RWt0vcCqfDxA,791
shapely/geometry/__pycache__/__init__.cpython-312.pyc,,
shapely/geometry/__pycache__/base.cpython-312.pyc,,
shapely/geometry/__pycache__/collection.cpython-312.pyc,,
shapely/geometry/__pycache__/conftest.cpython-312.pyc,,
shapely/geometry/__pycache__/geo.cpython-312.pyc,,
shapely/geometry/__pycache__/linestring.cpython-312.pyc,,
shapely/geometry/__pycache__/multilinestring.cpython-312.pyc,,
shapely/geometry/__pycache__/multipoint.cpython-312.pyc,,
shapely/geometry/__pycache__/multipolygon.cpython-312.pyc,,
shapely/geometry/__pycache__/point.cpython-312.pyc,,
shapely/geometry/__pycache__/polygon.cpython-312.pyc,,
shapely/geometry/base.py,sha256=TEOzvEjE9t5nyS8qHK7AOcJ8qfWA6ObjX8KEUKIZCGU,35154
shapely/geometry/collection.py,sha256=HNwHVDEFW4u-Ryj_K6a_2t_nR1EFTk_UrXkceTTdvSE,1657
shapely/geometry/conftest.py,sha256=-LG93MMgYhTOXv1lGcFnZrAzemG85wr-Fa3gUD-LHXw,234
shapely/geometry/geo.py,sha256=1Kzhx22Cv4zWugS6ffkpNWuIj3RvlF1LVwOxVLKTBNY,4233
shapely/geometry/linestring.py,sha256=8_oqZEk3Ekcrmp74q33SQcZ_iCg-ZsoZr-luPb3QQJ4,6974
shapely/geometry/multilinestring.py,sha256=y1HRJcwRbGnWPdD85CLbxCV47jlrjdvIjq6P3oWScNs,2889
shapely/geometry/multipoint.py,sha256=tIzo-bPOho50dE4lw1ZsVxwXYhh3DQsfeMKmXP46XMQ,2778
shapely/geometry/multipolygon.py,sha256=sV-XI234fi9laJZpgI96xWyMKlMGwOQWVHCkZNxXo5A,3969
shapely/geometry/point.py,sha256=xnphZryrwErtcFM-1tvV5noXXep0hcdQ6tWI940DBSs,4342
shapely/geometry/polygon.py,sha256=NXRMb8CLZwpvRXRa0v3Z3uBoifqfBxMF6Px7rVgT4zo,11731
shapely/geos.py,sha256=ghhtomhZopFRo8VdXOGKoB-5l0IG688uaKkFBcidZ-k,230
shapely/io.py,sha256=kh2nc2niWaeEN1IJInhMG4Iird4maFA2mPYrifhgE54,13717
shapely/lib.cp312-win_amd64.pyd,sha256=TbwJHmvGXpm-V3ugKmOctDaFMHV9jZIIIuxo9ChaW4o,141312
shapely/linear.py,sha256=X_k6Ji_dM5QtdclqOwp3EJTXQudx7_eePzvaCAj93mw,7500
shapely/measurement.py,sha256=ms0X6r3HbL-RhNTJUmipTqsq-lb5qCxSW1IWC-uzayw,10053
shapely/ops.py,sha256=FX4I_jbmih0N0rRbC-AVuR1Qa1YaFbvvMwOocWdFGCk,26920
shapely/plotting.py,sha256=NW0_bNY-qmruVWHxVyvSzIghqp-9No3GW1QnPBzEO5c,6377
shapely/predicates.py,sha256=mcXx1qqiycASKs-W53ab2MtQsuk2pqDEHuR-n9IIHwA,34107
shapely/prepared.py,sha256=8W_PuJak9xCeeL9i5EaBNF0orR0pEYArw8NWHwocAIk,2479
shapely/set_operations.py,sha256=tn0azttmr4uxQkNgXcu6Qdfz8hfAQEV0wUH6BrP1MGI,18062
shapely/speedups.py,sha256=U4E8qG6LqBFf5zATe7UXTtm-nLavM_0HMgRy0y1g_g8,984
shapely/strtree.py,sha256=MA58IdnCTw10Cn-34f2G2VRyWvPe-mUAKZbdmYvJigg,20934
shapely/testing.py,sha256=ko65WHmmwulf2h41LiZoIY_zVySNNtKGsRSFh2yQ5s0,6517
shapely/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
shapely/tests/__pycache__/__init__.cpython-312.pyc,,
shapely/tests/__pycache__/common.cpython-312.pyc,,
shapely/tests/__pycache__/test_constructive.cpython-312.pyc,,
shapely/tests/__pycache__/test_coordinates.cpython-312.pyc,,
shapely/tests/__pycache__/test_creation.cpython-312.pyc,,
shapely/tests/__pycache__/test_creation_indices.cpython-312.pyc,,
shapely/tests/__pycache__/test_geometry.cpython-312.pyc,,
shapely/tests/__pycache__/test_io.cpython-312.pyc,,
shapely/tests/__pycache__/test_linear.cpython-312.pyc,,
shapely/tests/__pycache__/test_measurement.cpython-312.pyc,,
shapely/tests/__pycache__/test_misc.cpython-312.pyc,,
shapely/tests/__pycache__/test_plotting.cpython-312.pyc,,
shapely/tests/__pycache__/test_predicates.cpython-312.pyc,,
shapely/tests/__pycache__/test_ragged_array.cpython-312.pyc,,
shapely/tests/__pycache__/test_set_operations.cpython-312.pyc,,
shapely/tests/__pycache__/test_strtree.cpython-312.pyc,,
shapely/tests/__pycache__/test_testing.cpython-312.pyc,,
shapely/tests/common.py,sha256=taRiHIhmKuD9U7GSOW6wZ8QWYf1jYd3MtQbz8-JMI34,3717
shapely/tests/geometry/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
shapely/tests/geometry/__pycache__/__init__.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_collection.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_coords.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_decimal.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_emptiness.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_equality.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_format.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_geometry_base.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_hash.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_linestring.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_multi.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_multilinestring.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_multipoint.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_multipolygon.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_point.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_polygon.cpython-312.pyc,,
shapely/tests/geometry/test_collection.py,sha256=_FbCZ2LRixSqjo3ZBAOQpv6b0VEriy_s3sMt-IQ6xGM,2312
shapely/tests/geometry/test_coords.py,sha256=bFBk5tOk9xA85Or_xxytqE6amJJuVSqqgDcFOvmsTEE,3538
shapely/tests/geometry/test_decimal.py,sha256=hnwjn0JotC_Ra61f_vyU4jjCa2ogaba_LKFa24piPt0,2867
shapely/tests/geometry/test_emptiness.py,sha256=y-5qtOovLKCiMU0weM3uVc19bXjw2oYm157jKU5hgig,2460
shapely/tests/geometry/test_equality.py,sha256=dyjPUIGzTlhVV8gZrshP_I3WJMlcbNPPQJdWNyEcsF4,8219
shapely/tests/geometry/test_format.py,sha256=Ap3lwD7WANOrb7wXrUeNaNAvtC36iEDWyAE1h6KO47Q,4673
shapely/tests/geometry/test_geometry_base.py,sha256=yar8e-KHXKd2AoYdN3WkwoPickyDvMFzNE4UlxSyHFI,7276
shapely/tests/geometry/test_hash.py,sha256=qR-Sqlcs3cCzHKsL3YC5U1-duF6X80C7wKsMW2mdBmU,701
shapely/tests/geometry/test_linestring.py,sha256=9UwWZPXTNvhUvVkGJp3qOxFjAj5Oe7WiHZMvLxTCg_4,6552
shapely/tests/geometry/test_multi.py,sha256=3zBwYn5P5GXQ-ZAF3T5FP0VFUUu6sO3BJd1woSRUCDI,315
shapely/tests/geometry/test_multilinestring.py,sha256=Z9hCbAJzQa4LhsoOyPjugQxA3OkJG1dIp4ekeS6G5GU,2913
shapely/tests/geometry/test_multipoint.py,sha256=DImu35fA9B7ZybaCkvsFtvQfU6_HrYXe9Z7G9a2WKeM,2513
shapely/tests/geometry/test_multipolygon.py,sha256=7xuVm5BtN1aKnlJZXIyyv1z3rv2KcVvk5JNJipdjoj4,4208
shapely/tests/geometry/test_point.py,sha256=4Tx502E5KKgWIG4IqQ67nsok2_GXB51BivZS0IF6cWw,4966
shapely/tests/geometry/test_polygon.py,sha256=PdUZwahhc7A1AsI5g6Grf5UMOJdS6h7D0k7rxS-o8j0,15756
shapely/tests/legacy/__init__.py,sha256=DvNYqlb6Fdp6ET8NneEufrJ1tHr4-TGJLbKRT0irvGQ,285
shapely/tests/legacy/__pycache__/__init__.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/conftest.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_affinity.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_box.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_buffer.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_cga.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_clip_by_rect.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_create_inconsistent_dimensionality.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_delaunay.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_empty_polygons.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_equality.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_geointerface.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_invalid_geometries.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_linear_referencing.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_linemerge.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_locale.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_make_valid.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_mapping.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_minimum_clearance.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_ndarrays.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_nearest.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_operations.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_operators.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_orient.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_parallel_offset.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_persist.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_pickle.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_polygonize.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_polylabel.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_predicates.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_prepared.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_products_z.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_shape.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_shared_paths.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_singularity.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_snap.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_split.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_substring.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_svg.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_transform.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_union.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_validation.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_vectorized.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_voronoi_diagram.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_wkb.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_wkt.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/threading_test.cpython-312.pyc,,
shapely/tests/legacy/conftest.py,sha256=TqT4HFT1QbhVu6VSm3ZnPUGBx5My80_TDtqn4LE9RN8,609
shapely/tests/legacy/test_affinity.py,sha256=_VhkAHJ1ym2YdHSPh0to6hAel2tXxfM3nvZZ6DryK-U,12842
shapely/tests/legacy/test_box.py,sha256=SUlhrgWOIXkE38pNPO0e2nabDFXD1mLBVze7IniXO8E,619
shapely/tests/legacy/test_buffer.py,sha256=kJcsyqcvE1CdjGMytVRlpPmDqrofYk7aROYOm09luHs,6693
shapely/tests/legacy/test_cga.py,sha256=YHYmzUiUhML4SEF67GmxiWdpyP32ZS1hbFYZZr7RQlM,1560
shapely/tests/legacy/test_clip_by_rect.py,sha256=zBID16XD31PYB0Ojls9x-8UTzhjBEL-9CqFYSszz5Vg,4180
shapely/tests/legacy/test_create_inconsistent_dimensionality.py,sha256=fsZuZLRCW_KhT0H4x6JiygeBK-v4WkRUafQY7NDsg1U,10858
shapely/tests/legacy/test_delaunay.py,sha256=XsoaoB5NpKHf_njWFpLO8aMiJTWwWwPf7F-ZAeQRemY,870
shapely/tests/legacy/test_empty_polygons.py,sha256=NGwmu47YMEIlUyhMu6-0NhTwpndpb10d8IOPjZzEj8U,705
shapely/tests/legacy/test_equality.py,sha256=P6npgs1XO49qgCGfF4tKjRvRNwZ2MrzJliqxXr3_1eM,963
shapely/tests/legacy/test_geointerface.py,sha256=nOVut4xdBtzhcUtZ1nYCUIfTLtUb2pf0rzBNiHbakhs,3766
shapely/tests/legacy/test_invalid_geometries.py,sha256=GiN9B-4-u2B4SkM559yJJsRrRSYVy6QFkZEb_XE-PCI,935
shapely/tests/legacy/test_linear_referencing.py,sha256=4Dk1w-IqvDz7qscb3ehnn7ytOo5c6_4PKI6juTsxFK4,2986
shapely/tests/legacy/test_linemerge.py,sha256=1mz2FIVG23w8UaTCl8hyxkCa-9SSUg5yVWVh5zBfMfY,1374
shapely/tests/legacy/test_locale.py,sha256=pecVCZlP8Rb2Pdl5n360zN6NQ-sxnr_jJq4mMl0OgOU,1492
shapely/tests/legacy/test_make_valid.py,sha256=7KvLFwuVoQSMJogu02lCBhjpGkU-nIQHLzphBs8gGMc,592
shapely/tests/legacy/test_mapping.py,sha256=OWDN_bbad5PN8jedKU8YcM_1vks3P90Ynl7fnoP4vZE,409
shapely/tests/legacy/test_minimum_clearance.py,sha256=EvzAV9U1OMZLleRaDHfrWaEaUvTGQHI6IHzVaWUPjNc,956
shapely/tests/legacy/test_ndarrays.py,sha256=CJqtQswPW_5zJbNvyWrAi8DgIYtbrgGJN4WqaUhlS-o,1286
shapely/tests/legacy/test_nearest.py,sha256=t2laaDOzrLp0N3kod_dAsVE2Vh55A7v3iE_eNKFIujs,494
shapely/tests/legacy/test_operations.py,sha256=ZVfAzthZCcH1J86o_zeqhGicCHjICB1beMF6SQMQhO4,4389
shapely/tests/legacy/test_operators.py,sha256=6tBr4jguWQfvCN0PHKT7c75cQ8VuzhRatTyClT2ha1Q,2008
shapely/tests/legacy/test_orient.py,sha256=ng3zbkE7TvoNxroAa9A6j9KIAOdIKio7eMmYY5lV3yA,2464
shapely/tests/legacy/test_parallel_offset.py,sha256=PTgQI0YRTmVOZ3hJvA8mbIarv6V1ey_PvYQDwETFACg,2392
shapely/tests/legacy/test_persist.py,sha256=7UV9X0VKN0JVg82z-LIusNTJUSXghx1MhOm3ofpxCIY,1764
shapely/tests/legacy/test_pickle.py,sha256=IychV1N9dzaVb_854K49Vcvhpkb_QaD4CvsJnOUlVBU,2565
shapely/tests/legacy/test_polygonize.py,sha256=-WKB6uOF5r8dlbO9q1oxJH8xU4naE6VqO6IJw8tbjTY,1433
shapely/tests/legacy/test_polylabel.py,sha256=P4lNlI-KofyQnHgBb3OR5BQ4JLvWXH3xXSEjFVO5D5A,3322
shapely/tests/legacy/test_predicates.py,sha256=1XZCLpW4LzcGb_KVIIOC83a-hOVtfUYKxBN3oP6G3LY,3061
shapely/tests/legacy/test_prepared.py,sha256=sIjXYCv8nkX4_yHJBJfBqfAE6VXVaLPtqcKH2gbl6UA,2409
shapely/tests/legacy/test_products_z.py,sha256=ZUIwvBhnb1E7wnSHmQMYEznDAUDDe6ZrpqTzb3zOR_Q,401
shapely/tests/legacy/test_shape.py,sha256=HG1UEtF5ZTHm3U0I4CjI0nu5mOYDUjT-CEHYc839zUk,1452
shapely/tests/legacy/test_shared_paths.py,sha256=C85sQrc6LWkGX1UK70MnGXagJOYdTvehdfCfOivF-sc,1479
shapely/tests/legacy/test_singularity.py,sha256=1RA1VwOpmbYyd8Z51L8WZP2eDEG1r9VbSga4jZSIhOg,396
shapely/tests/legacy/test_snap.py,sha256=ucz6y6zzU5TMrG3bBoeRQ9EJcFDNKvOZbOGjk1wRSIc,789
shapely/tests/legacy/test_split.py,sha256=EsijsUYkqPZVDRpmdr0HSi1pxvjrUP4qY4FbJoN6DDE,9963
shapely/tests/legacy/test_substring.py,sha256=pp1is7_UjfvW_ruzUeY9h5K55OJsQvpDc71s7OID9F8,21368
shapely/tests/legacy/test_svg.py,sha256=k6t_Swtcxm2x0IpXzfmJmWPyEUuYmXA1ctEVEhb6bFU,8466
shapely/tests/legacy/test_transform.py,sha256=jqcm1hgDhEibUvsjyPEo0nhMrHvyfK0-NiJ5-k3A9Bk,2785
shapely/tests/legacy/test_union.py,sha256=y1Uw1LnTjq0CZ2wkz08_nHIqnWMFPMFwftWVigNE7Ig,2389
shapely/tests/legacy/test_validation.py,sha256=pF143oq5dwri0wgirycIwTDeXGct5Y0gfqMRHewFPfM,247
shapely/tests/legacy/test_vectorized.py,sha256=3R5IJzefQhbzRZT3a8bTw-aDfAUc8GNbMuQKKNl6gsU,3688
shapely/tests/legacy/test_voronoi_diagram.py,sha256=xc72EOgYw8r_D1C135RWfX4MgdJAxHoXk0rZGJq7YKU,4590
shapely/tests/legacy/test_wkb.py,sha256=OmSOBQxUzeQ5xAFzSMxjPlDLD4zkUu8Qofa788RlxFw,6314
shapely/tests/legacy/test_wkt.py,sha256=hZ6lyLeu9hniz9clORl1S_4A2z1_n9qj1oPAu0yxNAc,1671
shapely/tests/legacy/threading_test.py,sha256=mjm1Kk6uAGCyQm24xHuLx6x8oAE49G7pE75Oa25XDJE,1076
shapely/tests/test_constructive.py,sha256=3LK-UPPstYstIydXfBAaYeIuwTw6syC5C6pdA3LZPZ0,34034
shapely/tests/test_coordinates.py,sha256=a-M5pv9yFhKi7Y9cu0ZOh3VAINu2PXht5FeUTF4sLbU,9334
shapely/tests/test_creation.py,sha256=9DB4qGj2e_QHBOCMZqhTsy5q1m-FB_48tGR1r3ZtbXM,18462
shapely/tests/test_creation_indices.py,sha256=tEilLKC8BV7-1tCpV13OyuExY4Q2hiqkXc-N0gKzuk4,13816
shapely/tests/test_geometry.py,sha256=xzaLBJrxiKQIGYH5s5OVgpwjgDKaL1srOqCutdIrG7s,25192
shapely/tests/test_io.py,sha256=_HQfTZ_M_pTglskyVSDd-5pIoQy87u5gA3PNPRkr_NA,26008
shapely/tests/test_linear.py,sha256=vwZAEyRhRwJa9PUcSiw3oVwHIg0LVV-B8Q3qqFv-YbM,7653
shapely/tests/test_measurement.py,sha256=eZMBCwB-T--omSC_BsM__s2Miha17ACcn4GBN80FToI,11128
shapely/tests/test_misc.py,sha256=lcoz0HTk2CD5UNzDYaawpW_HKi-_Sh-dGYlXMxqzpsM,5797
shapely/tests/test_plotting.py,sha256=gqvCIxCMloTHdSfhHyChRq8uHPHy_CJMY1R3ee1XQDw,3718
shapely/tests/test_predicates.py,sha256=EL9QG4H6bsmPXRzlpaOtUuD5tE1qevsFPt58r3-2Vgc,11312
shapely/tests/test_ragged_array.py,sha256=AT2TZzE4uR1nilNMLuiOqLgKIyiz1-_b1--7DHB1WwE,12352
shapely/tests/test_set_operations.py,sha256=EjIzripadGFf8m74_bjZScuta7pDQb4-TdGaeT3eDPw,17376
shapely/tests/test_strtree.py,sha256=xxsOJvdRUtJjolGv1yKSrUGMPxGeEdx45EkPJeNcuAA,76386
shapely/tests/test_testing.py,sha256=ktDwrQqLMQqGlcgiUCYBVQ-8frhsZ0foJfga_DUmHl4,3175
shapely/validation.py,sha256=SCsQXraJ1Gw9YCJQHsSZ_q9jTX8893KHmNXc3iRaPlA,1488
shapely/vectorized/__init__.py,sha256=YPI7TBuRWcKSdQ_nWLHljIWH-fgVlWKfJTqHHOCILo4,2324
shapely/vectorized/__pycache__/__init__.cpython-312.pyc,,
shapely/wkb.py,sha256=6bbd3HLsgJyX3ooV-L3xB9q0_j6q1DQ-_cEh1sy3j7A,1976
shapely/wkt.py,sha256=-_Tn8HWFw568m-EMQLdSivzeSWuBu_36F7r3OtLTg1A,2034
