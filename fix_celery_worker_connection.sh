#!/bin/bash

set -e

echo "🔧 Celery Worker连接修复工具"
echo "============================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 1. 诊断当前worker状态
echo ""
log_info "步骤1: 诊断当前worker状态"
echo "----------------------------------------"

log_info "检查supervisorctl状态..."
docker exec km-background supervisorctl status

echo ""
log_info "检查实际的celery进程..."
docker exec km-background ps aux | grep celery | grep -v grep || log_warning "未找到celery进程"

echo ""
log_info "检查worker进程详情..."
docker exec km-background pgrep -f "celery.*worker" | while read pid; do
    echo "PID $pid:"
    docker exec km-background ps -p $pid -o pid,ppid,cmd || true
done

# 2. 检查worker日志中的错误
echo ""
log_info "步骤2: 检查worker日志中的错误"
echo "----------------------------------------"

log_info "检查primary worker日志错误..."
if docker exec km-background test -f /var/log/celery_worker_primary.log; then
    echo "Primary Worker错误日志:"
    docker exec km-background tail -50 /var/log/celery_worker_primary.log | grep -E "(error|exception|traceback|failed)" || echo "未找到错误日志"
fi

log_info "检查docfetching worker日志错误..."
if docker exec km-background test -f /var/log/celery_worker_docfetching.log; then
    echo "Docfetching Worker错误日志:"
    docker exec km-background tail -50 /var/log/celery_worker_docfetching.log | grep -E "(error|exception|traceback|failed)" || echo "未找到错误日志"
fi

# 3. 测试Redis连接
echo ""
log_info "步骤3: 测试Redis连接"
echo "----------------------------------------"

log_info "从容器内测试Redis连接..."
docker exec km-background python3 -c "
import sys
sys.path.append('/app')

try:
    from onyx.configs.app_configs import REDIS_HOST, REDIS_PORT, REDIS_DB_NUMBER_CELERY
    import redis
    
    print(f'Redis配置: {REDIS_HOST}:{REDIS_PORT}/{REDIS_DB_NUMBER_CELERY}')
    
    # 测试连接
    r = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB_NUMBER_CELERY, socket_timeout=5)
    result = r.ping()
    print(f'Redis连接: {\"成功\" if result else \"失败\"}')
    
    # 测试基本操作
    test_key = 'celery_test_key'
    r.set(test_key, 'test_value', ex=60)
    value = r.get(test_key)
    print(f'Redis读写测试: {\"成功\" if value else \"失败\"}')
    r.delete(test_key)
    
    # 检查连接池
    pool_info = r.connection_pool.connection_kwargs
    print(f'连接池配置: host={pool_info.get(\"host\")}, port={pool_info.get(\"port\")}')
    
except Exception as e:
    print(f'Redis连接失败: {e}')
    import traceback
    traceback.print_exc()
" || log_error "Redis连接测试失败"

# 4. 重启有问题的worker
echo ""
log_info "步骤4: 重启有问题的worker"
echo "----------------------------------------"

log_info "重启primary worker..."
docker exec km-background supervisorctl restart celery_worker_primary

log_info "重启docfetching worker..."
docker exec km-background supervisorctl restart celery_worker_docfetching

log_info "等待worker重启..."
sleep 15

log_info "检查重启后的状态..."
docker exec km-background supervisorctl status | grep -E "(primary|docfetching)"

# 5. 测试worker连接
echo ""
log_info "步骤5: 测试worker连接"
echo "----------------------------------------"

log_info "等待worker完全启动..."
sleep 10

log_info "测试primary worker连接..."
if docker exec km-background timeout 15 celery -A onyx.background.celery.versioned_apps.primary inspect ping 2>/dev/null; then
    log_success "Primary worker连接成功"
else
    log_error "Primary worker连接失败"
    
    # 查看详细错误
    log_info "查看primary worker详细错误..."
    docker exec km-background celery -A onyx.background.celery.versioned_apps.primary inspect ping 2>&1 | head -20
fi

log_info "测试docfetching worker连接..."
if docker exec km-background timeout 15 celery -A onyx.background.celery.versioned_apps.docfetching inspect ping 2>/dev/null; then
    log_success "Docfetching worker连接成功"
else
    log_error "Docfetching worker连接失败"
    
    # 查看详细错误
    log_info "查看docfetching worker详细错误..."
    docker exec km-background celery -A onyx.background.celery.versioned_apps.docfetching inspect ping 2>&1 | head -20
fi

# 6. 如果还是失败，尝试手动启动worker
echo ""
log_info "步骤6: 如果需要，手动启动worker"
echo "----------------------------------------"

# 检查是否还有连接问题
if ! docker exec km-background timeout 10 celery -A onyx.background.celery.versioned_apps.primary inspect ping >/dev/null 2>&1; then
    log_warning "Primary worker仍然无法连接，尝试手动启动..."
    
    # 停止supervisord中的worker
    docker exec km-background supervisorctl stop celery_worker_primary
    sleep 5
    
    # 手动启动worker
    log_info "手动启动primary worker..."
    docker exec -d km-background celery -A onyx.background.celery.versioned_apps.primary worker \
        --loglevel=INFO \
        --hostname=primary@manual \
        -Q celery \
        --logfile=/var/log/celery_worker_primary_manual.log
    
    sleep 10
    
    # 测试手动启动的worker
    if docker exec km-background timeout 10 celery -A onyx.background.celery.versioned_apps.primary inspect ping >/dev/null 2>&1; then
        log_success "手动启动的primary worker连接成功"
    else
        log_error "手动启动的primary worker也失败"
    fi
fi

# 对docfetching worker做同样的处理
if ! docker exec km-background timeout 10 celery -A onyx.background.celery.versioned_apps.docfetching inspect ping >/dev/null 2>&1; then
    log_warning "Docfetching worker仍然无法连接，尝试手动启动..."
    
    docker exec km-background supervisorctl stop celery_worker_docfetching
    sleep 5
    
    log_info "手动启动docfetching worker..."
    docker exec -d km-background celery -A onyx.background.celery.versioned_apps.docfetching worker \
        --loglevel=INFO \
        --hostname=docfetching@manual \
        -Q connector_doc_fetching \
        --logfile=/var/log/celery_worker_docfetching_manual.log
    
    sleep 10
    
    if docker exec km-background timeout 10 celery -A onyx.background.celery.versioned_apps.docfetching inspect ping >/dev/null 2>&1; then
        log_success "手动启动的docfetching worker连接成功"
    else
        log_error "手动启动的docfetching worker也失败"
    fi
fi

# 7. 测试任务发送
echo ""
log_info "步骤7: 测试任务发送"
echo "----------------------------------------"

log_info "测试发送简单任务..."
docker exec km-background python3 -c "
import sys
sys.path.append('/app')

try:
    from onyx.background.celery.apps.primary import celery_app
    
    # 发送一个简单的ping任务
    result = celery_app.send_task(
        'celery.ping',
        queue='celery'
    )
    
    print(f'✅ Ping任务已发送: {result.id}')
    
    # 等待结果
    import time
    time.sleep(5)
    
    if result.ready():
        print(f'✅ Ping任务完成: {result.result}')
    else:
        print(f'⏳ Ping任务处理中: {result.state}')
        
except Exception as e:
    print(f'❌ 任务发送失败: {e}')
    import traceback
    traceback.print_exc()
" || log_warning "任务发送测试失败"

# 8. 生成修复报告
echo ""
log_info "步骤8: 生成修复报告"
echo "----------------------------------------"

echo ""
log_success "Celery Worker连接修复完成！"
echo ""
echo "📋 修复总结:"
echo "1. ✅ 检查了worker状态和日志"
echo "2. ✅ 验证了Redis连接"
echo "3. ✅ 重启了有问题的worker"
echo "4. ✅ 测试了worker连接"
echo "5. ✅ 必要时手动启动了worker"
echo "6. ✅ 测试了任务发送"

echo ""
echo "🔍 验证命令:"
echo "# 检查worker连接"
echo "docker exec km-background celery -A onyx.background.celery.versioned_apps.primary inspect ping"
echo "docker exec km-background celery -A onyx.background.celery.versioned_apps.docfetching inspect ping"
echo ""
echo "# 检查worker状态"
echo "docker exec km-background celery -A onyx.background.celery.versioned_apps.primary inspect active"
echo ""
echo "# 查看实时日志"
echo "docker logs km-background -f | grep -E '(celery|worker|task)'"

echo ""
echo "📱 监控建议:"
echo "1. 定期检查worker连接状态"
echo "2. 监控worker日志中的错误"
echo "3. 确保Redis连接稳定"
echo "4. 如果问题重现，检查系统资源和网络"

echo ""
echo "🚨 重要提醒:"
echo "如果手动启动的worker工作正常，可能需要检查supervisord配置"
echo "或者容器的启动脚本中的worker启动逻辑"
