#!/usr/bin/env python3
"""
Onyx Vespa应用包部署脚本
基于现有的backend/onyx/document_index/vespa/配置
"""

import os
import sys
import zipfile
import requests
import jinja2
from datetime import datetime, timedelta
import json
import time

# 添加backend路径到Python路径
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_path)

def create_hosts_xml():
    """创建hosts.xml文件"""
    return """<?xml version="1.0" encoding="utf-8" ?>
<hosts>
  <host name="localhost">
    <alias>danswer-node</alias>
  </host>
</hosts>"""

def render_services_xml():
    """渲染services.xml.jinja模板"""
    vespa_config_path = os.path.join('backend', 'onyx', 'document_index', 'vespa', 'app_config')
    services_jinja_path = os.path.join(vespa_config_path, 'services.xml.jinja')
    
    if not os.path.exists(services_jinja_path):
        raise FileNotFoundError(f"找不到services.xml.jinja文件: {services_jinja_path}")
    
    # 读取模板
    with open(services_jinja_path, 'r', encoding='utf-8') as f:
        template_str = f.read()
    
    # 创建Jinja环境
    jinja_env = jinja2.Environment()
    template = jinja_env.from_string(template_str)
    
    # 渲染模板
    # 使用默认的文档元素配置
    document_elements = '<document type="danswer_chunk" mode="index" />'
    num_search_threads = "4"  # 默认搜索线程数
    
    services_xml = template.render(
        document_elements=document_elements,
        num_search_threads=num_search_threads
    )
    
    return services_xml

def render_schema_file():
    """渲染danswer_chunk.sd.jinja模板"""
    vespa_config_path = os.path.join('backend', 'onyx', 'document_index', 'vespa', 'app_config')
    schema_jinja_path = os.path.join(vespa_config_path, 'schemas', 'danswer_chunk.sd.jinja')
    
    if not os.path.exists(schema_jinja_path):
        raise FileNotFoundError(f"找不到danswer_chunk.sd.jinja文件: {schema_jinja_path}")
    
    # 读取模板
    with open(schema_jinja_path, 'r', encoding='utf-8') as f:
        template_str = f.read()
    
    # 创建Jinja环境
    jinja_env = jinja2.Environment()
    template = jinja_env.from_string(template_str)
    
    # 渲染模板 - 使用默认配置
    schema_content = template.render(
        schema_name="danswer_chunk",
        embedding_precision="float",
        dim="1024",  # 默认嵌入维度
        multi_tenant=False
    )
    
    return schema_content

def render_validation_overrides():
    """渲染validation-overrides.xml.jinja模板"""
    vespa_config_path = os.path.join('backend', 'onyx', 'document_index', 'vespa', 'app_config')
    overrides_jinja_path = os.path.join(vespa_config_path, 'validation-overrides.xml.jinja')
    
    if not os.path.exists(overrides_jinja_path):
        raise FileNotFoundError(f"找不到validation-overrides.xml.jinja文件: {overrides_jinja_path}")
    
    # 读取模板
    with open(overrides_jinja_path, 'r', encoding='utf-8') as f:
        template_str = f.read()
    
    # 创建Jinja环境
    jinja_env = jinja2.Environment()
    template = jinja_env.from_string(template_str)
    
    # 设置过期时间为30天后（Vespa最大允许期限）
    until_date = (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")
    
    overrides_content = template.render(until_date=until_date)
    
    return overrides_content

def create_application_package():
    """创建Vespa应用包"""
    print("📦 创建Onyx Vespa应用包...")
    
    # 创建临时目录
    app_dir = "/tmp/onyx-vespa-app"
    os.makedirs(app_dir, exist_ok=True)
    os.makedirs(os.path.join(app_dir, "schemas"), exist_ok=True)
    
    try:
        # 1. 创建hosts.xml
        hosts_content = create_hosts_xml()
        with open(os.path.join(app_dir, "hosts.xml"), 'w', encoding='utf-8') as f:
            f.write(hosts_content)
        print("✅ 创建hosts.xml")
        
        # 2. 渲染并创建services.xml
        services_content = render_services_xml()
        with open(os.path.join(app_dir, "services.xml"), 'w', encoding='utf-8') as f:
            f.write(services_content)
        print("✅ 创建services.xml")
        
        # 3. 渲染并创建schema文件
        schema_content = render_schema_file()
        with open(os.path.join(app_dir, "schemas", "danswer_chunk.sd"), 'w', encoding='utf-8') as f:
            f.write(schema_content)
        print("✅ 创建danswer_chunk.sd")
        
        # 4. 渲染并创建validation-overrides.xml
        overrides_content = render_validation_overrides()
        with open(os.path.join(app_dir, "validation-overrides.xml"), 'w', encoding='utf-8') as f:
            f.write(overrides_content)
        print("✅ 创建validation-overrides.xml")
        
        # 5. 创建ZIP包
        zip_path = "/tmp/onyx-application.zip"
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(app_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, app_dir)
                    zipf.write(file_path, arcname)
        
        print(f"✅ 应用包已打包: {zip_path}")
        return zip_path
        
    except Exception as e:
        print(f"❌ 创建应用包失败: {e}")
        raise

def deploy_application_package(zip_path):
    """部署应用包到Vespa"""
    print("🚀 部署应用包到Vespa...")
    
    deploy_url = "http://localhost:19071/application/v2/tenant/default/prepareandactivate"
    
    try:
        with open(zip_path, 'rb') as f:
            files = {'applicationZip': f}
            headers = {'Content-Type': 'application/zip'}
            
            print(f"正在部署到: {deploy_url}")
            response = requests.post(
                deploy_url,
                data=f.read(),
                headers=headers,
                timeout=300
            )
        
        if response.status_code == 200:
            print("✅ 应用包部署成功")
            return True
        else:
            print(f"❌ 部署失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 部署过程中出错: {e}")
        return False

def wait_for_application_ready():
    """等待应用就绪"""
    print("⏳ 等待Vespa应用就绪...")
    
    max_wait = 300  # 5分钟
    elapsed = 0
    
    while elapsed < max_wait:
        try:
            # 检查ApplicationStatus
            response = requests.get("http://localhost:8081/ApplicationStatus", timeout=10)
            if response.status_code == 200:
                print("✅ Vespa应用已就绪")
                return True
        except:
            pass
        
        time.sleep(10)
        elapsed += 10
        print(f"等待中... {elapsed}/{max_wait}秒")
    
    print("❌ 等待应用就绪超时")
    return False

def verify_deployment():
    """验证部署结果"""
    print("🔍 验证部署结果...")
    
    try:
        # 测试ApplicationStatus
        response = requests.get("http://localhost:8081/ApplicationStatus", timeout=10)
        print(f"ApplicationStatus: {response.status_code}")
        
        # 测试基本连通性
        response = requests.get("http://localhost:8081/", timeout=10)
        print(f"基本连通性: {response.status_code}")
        
        # 测试搜索API
        search_url = "http://localhost:8081/search/"
        params = {
            "yql": "select * from danswer_chunk where true limit 1",
            "hits": 1
        }
        response = requests.get(search_url, params=params, timeout=10)
        print(f"搜索API: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"验证过程中出错: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Onyx Vespa应用包部署工具")
    print("=" * 40)
    
    try:
        # 1. 创建应用包
        zip_path = create_application_package()
        
        # 2. 部署应用包
        if deploy_application_package(zip_path):
            # 3. 等待应用就绪
            if wait_for_application_ready():
                # 4. 验证部署
                verify_deployment()
                print("\n🎉 Onyx Vespa应用包部署完成！")
                print("现在可以重启其他服务了：")
                print("docker-compose -f docker-compose.main.yml restart background api_server")
            else:
                print("\n❌ 应用未能在预期时间内就绪")
                return 1
        else:
            print("\n❌ 应用包部署失败")
            return 1
            
    except Exception as e:
        print(f"\n❌ 部署过程中发生错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
